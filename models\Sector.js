const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Sector = sequelize.define(
  "Sector",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    name: { type: DataTypes.STRING(100), allowNull: false },
    created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
  },
  { tableName: "sectors", timestamps: false }
);

module.exports = Sector;
