const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Department = sequelize.define(
  "Department",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    name: { type: DataTypes.STRING(100), allowNull: false },
    name_en: { type: DataTypes.STRING(100), allowNull: true },
    name_am: { type: DataTypes.STRING(100), allowNull: true },
    name_af: { type: DataTypes.STRING(100), allowNull: true },
    code: { type: DataTypes.STRING(20), allowNull: true, unique: true },
    division_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "divisions",
        key: "id",
      },
    },
    description_en: { type: DataTypes.TEXT, allowNull: true },
    description_am: { type: DataTypes.TEXT, allowNull: true },
    description_af: { type: DataTypes.TEXT, allowNull: true },
    created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
  },
  {
    tableName: "departments",
    timestamps: false,
    indexes: [
      { fields: ["division_id"] },
      { fields: ["name"] },
    ],
  }
);

module.exports = Department;
