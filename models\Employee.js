const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Employee = sequelize.define(
  "Employee",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    first_name_en: { type: DataTypes.STRING(50), allowNull: true },
    first_name_am: { type: DataTypes.STRING(50), allowNull: true },
    first_name_af: { type: DataTypes.STRING(50), allowNull: true },
    middle_name_en: { type: DataTypes.STRING(50), allowNull: true },
    middle_name_am: { type: DataTypes.STRING(50), allowNull: true },
    middle_name_af: { type: DataTypes.STRING(50), allowNull: true },
    last_name_en: { type: DataTypes.STRING(50), allowNull: true },
    last_name_am: { type: DataTypes.STRING(50), allowNull: true },
    last_name_af: { type: DataTypes.STRING(50), allowNull: true },
    office_number: { type: DataTypes.STRING(10), allowNull: true },
    floor_number: { type: DataTypes.INTEGER, allowNull: true },
    position_en: { type: DataTypes.STRING(100), allowNull: true },
    position_am: { type: DataTypes.STRING(100), allowNull: true },
    position_af: { type: DataTypes.STRING(100), allowNull: true },
    department_en: { type: DataTypes.STRING(100), allowNull: true },
    department_am: { type: DataTypes.STRING(100), allowNull: true },
    department_af: { type: DataTypes.STRING(100), allowNull: true },

    // Organizational hierarchy foreign keys
    sector_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "sectors",
        key: "id",
      },
    },
    division_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "divisions",
        key: "id",
      },
    },
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "departments",
        key: "id",
      },
    },
    team_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "teams",
        key: "id",
      },
    },

    section: { type: DataTypes.STRING, allowNull: false },
    city: { type: DataTypes.STRING(100), allowNull: true },
    subcity: { type: DataTypes.STRING(100), allowNull: true },
    profile_picture: { type: DataTypes.STRING, allowNull: true },
    created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
  },
  {
    tableName: "employees",
    timestamps: false,
    indexes: [
      { fields: ["sector_id"] },
      { fields: ["division_id"] },
      { fields: ["department_id"] },
      { fields: ["team_id"] },
      { fields: ["section"] },
    ],
  }
);

module.exports = Employee;
