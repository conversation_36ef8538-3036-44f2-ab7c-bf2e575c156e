const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Employee = sequelize.define(
  "Employee",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    first_name_en: { type: DataTypes.STRING(50), allowNull: true },
    first_name_am: { type: DataTypes.STRING(50), allowNull: true },
    first_name_af: { type: DataTypes.STRING(50), allowNull: true },
    middle_name_en: { type: DataTypes.STRING(50), allowNull: true },
    middle_name_am: { type: DataTypes.STRING(50), allowNull: true },
    middle_name_af: { type: DataTypes.STRING(50), allowNull: true },
    last_name_en: { type: DataTypes.STRING(50), allowNull: true },
    last_name_am: { type: DataTypes.STRING(50), allowNull: true },
    last_name_af: { type: DataTypes.STRING(50), allowNull: true },
    office_number: { type: DataTypes.STRING(10), allowNull: true },
    floor_number: { type: DataTypes.INTEGER, allowNull: true },
    position_en: { type: DataTypes.STRING(100), allowNull: true },
    position_am: { type: DataTypes.STRING(100), allowNull: true },
    position_af: { type: DataTypes.STRING(100), allowNull: true },
    department_en: { type: DataTypes.STRING(100), allowNull: true },
    department_am: { type: DataTypes.STRING(100), allowNull: true },
    department_af: { type: DataTypes.STRING(100), allowNull: true },
    section: { type: DataTypes.STRING, allowNull: false },
    city: { type: DataTypes.STRING(100), allowNull: true },
    subcity: { type: DataTypes.STRING(100), allowNull: true },
    profile_picture: { type: DataTypes.STRING, allowNull: true },
    created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
  },
  { tableName: "employees", timestamps: false }
);

module.exports = Employee;
