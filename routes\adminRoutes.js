const express = require("express");
const router = express.Router();
const upload = require("../middleware/upload");
const { authenticateToken, restrictTo } = require("../middleware/auth");

const {
  adminLogin,
  createAdmin,
  updateAdminProfile,
  requestPasswordReset,
  resetPassword,
  getStatistics,
  getDepartments,
  logAdmins,
  exportReport,
  exportEmployees,
  exportComplaints,
  exportFeedback,
  getComplaintTrends,
  getLocationHierarchy,
  getStatisticsWithLocation,
  getRatingsAdmin,
  getPublicRatingsAdmin,
  respondToFeedback,
  respondToPublicFeedback,
} = require("../controllers/adminController");

const {
  getEmployeesAdmin,
  createEmployee,
  updateEmployee,
  deleteEmployee,
} = require("../controllers/employeeController");

const {
  getComplaintsAdmin,
  resolveComplaint,
} = require("../controllers/complaintController");

const { getFeedbackAdmin } = require("../controllers/feedbackController");

const publicAdminController = require("../controllers/publicAdminController");

// Authentication routes
router.post("/login", adminLogin);
router.post("/request-password-reset", requestPasswordReset);
router.post("/reset-password", resetPassword);

// Admin management routes (SuperAdmin only)
router.post(
  "/admins",
  authenticateToken,
  restrictTo("SuperAdmin"),
  upload.single("profile_picture"),
  createAdmin
);
router.get(
  "/log-admins",
  authenticateToken,
  restrictTo("SuperAdmin"),
  logAdmins
);

// Profile management
router.put(
  "/profile",
  authenticateToken,
  upload.single("profile_picture"),
  updateAdminProfile
);

// Employee management routes (Admin access required)
router.get(
  "/employees",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getEmployeesAdmin
);
router.post(
  "/employees",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  upload.single("profile_picture"),
  createEmployee
);
router.put(
  "/employees/:id",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  upload.single("profile_picture"),
  updateEmployee
);
router.delete(
  "/employees/:id",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  deleteEmployee
);

// Employee hierarchy filtering routes
router.use("/employees", require("./employeeHierarchy"));

// Complaint management routes
router.get(
  "/complaints",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getComplaintsAdmin
);
router.get(
  "/complaints/trends",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getComplaintTrends
);
router.put(
  "/complaints/:id/resolve",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  resolveComplaint
);

// Feedback management routes
router.get(
  "/feedback",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getFeedbackAdmin
);
router.put(
  "/feedback/:id/respond",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  respondToFeedback
);

// Ratings management routes
router.get(
  "/ratings",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getRatingsAdmin
);
router.get(
  "/public/ratings",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getPublicRatingsAdmin
);

// Statistics and reports
router.get(
  "/statistics",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getStatistics
);
router.get(
  "/export-report",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  exportReport
);

// Specific export routes
router.get(
  "/employees/export",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  exportEmployees
);
router.get(
  "/complaints/export",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  exportComplaints
);
router.get(
  "/feedback/export",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  exportFeedback
);

// Utility routes
router.get(
  "/departments",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin"),
  getDepartments
);

// New hierarchical location routes
router.get(
  "/location-hierarchy",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getLocationHierarchy
);

router.get(
  "/statistics-location",
  authenticateToken,
  restrictTo("SuperAdmin", "SubCityAdmin", "Admin"),
  getStatisticsWithLocation
);

// =================================
// PUBLIC CITIZEN SUBMISSIONS MANAGEMENT
// =================================

// Public Complaints Management
router.get(
  "/public/complaints",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.getPublicComplaints
);

router.get(
  "/public/complaints/:id",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.getPublicComplaint
);

router.put(
  "/public/complaints/:id/status",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.updateComplaintStatus
);

router.post(
  "/public/complaints/:id/response",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.addComplaintResponse
);

// Public Ratings Management
router.get(
  "/public/ratings",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.getPublicRatings
);

router.get(
  "/public/ratings/analytics",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.getRatingsAnalytics
);

// Public Feedback Management
router.get(
  "/public/feedback",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.getPublicFeedback
);

router.post(
  "/public/feedback/:id/response",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.respondToFeedback
);
router.put(
  "/public/feedback/:id/respond",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  respondToPublicFeedback
);

router.put(
  "/public/feedback/:id/status",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.updateFeedbackStatus
);

// Department & Office Management
router.post(
  "/public/departments",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin"),
  publicAdminController.createDepartment
);

router.post(
  "/public/offices",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin"),
  publicAdminController.createOffice
);

// Dashboard Statistics for Public Data
router.get(
  "/public/dashboard-stats",
  authenticateToken,
  restrictTo("SuperAdmin", "Admin", "SubCityAdmin"),
  publicAdminController.getDashboardStats
);

module.exports = router;
