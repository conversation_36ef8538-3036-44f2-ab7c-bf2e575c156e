const { body, validationResult } = require("express-validator");
const multer = require("multer");
const path = require("path");
const crypto = require("crypto");
const { Op } = require("sequelize");

const {
  PublicComplaint,
  PublicRating,
  PublicFeedback,
  Department,
  Office,
  Employee,
  Sector,
  Division,
} = require("../models");

// Helper function to generate tracking codes
const generateTrackingCode = () => {
  const prefix = "TC";
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 5).toUpperCase();
  return `${prefix}-${timestamp}-${random}`;
};

// Helper function to generate reference numbers
const generateReferenceNumber = () => {
  const prefix = "FB";
  const year = new Date().getFullYear();
  const random = Math.random().toString().padStart(5, "0").slice(-5);
  return `${prefix}-${year}-${random}`;
};

// Configure multer for voice complaints
const voiceStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "Uploads/voice_complaints/");
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, "voice-" + uniqueSuffix + path.extname(file.originalname));
  },
});

const voiceUpload = multer({
  storage: voiceStorage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /mp3|wav|m4a|ogg|webm/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error("Only audio files are allowed"));
    }
  },
});

// Validation rules
const complaintValidation = [
  body("complainant_name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2-100 characters"),
  body("phone_number")
    .matches(/^[\+]?[0-9\-\s]+$/)
    .isLength({ min: 9, max: 15 })
    .withMessage("Invalid phone number format"),
  body("sub_city")
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("Sub-city is required"),
  body("kebele")
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage("Kebele is required"),
  body("complaint_description")
    .trim()
    .isLength({ min: 10 })
    .withMessage("Complaint description must be at least 10 characters"),
  body("department").trim().notEmpty().withMessage("Department is required"),
  body("office").trim().notEmpty().withMessage("Office is required"),
  body("desired_action")
    .trim()
    .isLength({ min: 10 })
    .withMessage("Desired action must be at least 10 characters"),
];

const ratingValidation = [
  body("department").trim().notEmpty().withMessage("Department is required"),
  body("overall_rating")
    .isInt({ min: 1, max: 5 })
    .withMessage("Overall rating must be 1-5"),
  body("courtesy_rating")
    .isInt({ min: 1, max: 5 })
    .withMessage("Courtesy rating must be 1-5"),
  body("timeliness_rating")
    .isInt({ min: 1, max: 5 })
    .withMessage("Timeliness rating must be 1-5"),
  body("knowledge_rating")
    .isInt({ min: 1, max: 5 })
    .withMessage("Knowledge rating must be 1-5"),
  body("citizen_name").optional().trim().isLength({ min: 2 }),
  body("citizen_phone").optional().trim().isLength({ min: 9 }),
];

const feedbackValidation = [
  body("full_name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2-100 characters"),
  body("email").optional().isEmail().withMessage("Invalid email address"),
  body("phone_number").optional().trim().isLength({ min: 9 }),
  body("service_type")
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage("Service type is required"),
  body("feedback_type")
    .isIn(["complaint", "suggestion", "compliment"])
    .withMessage("Invalid feedback type"),
  body("message")
    .trim()
    .isLength({ min: 10 })
    .withMessage("Message must be at least 10 characters"),
];

const publicController = {
  // =================================
  // DEPARTMENTS & OFFICES
  // =================================

  // Get all active departments
  getDepartments: async (req, res) => {
    try {
      const departments = await Department.findAll({
        attributes: ["id", "name_en", "name_am", "description_en"],
        order: [["name_en", "ASC"]],
      });

      // Transform the data to match frontend expectations
      const transformedDepts = departments.map((dept) => ({
        id: dept.id,
        name: dept.name_en,
        name_amharic: dept.name_am,
        description: dept.description_en,
      }));

      if (process.env.NODE_ENV === "development") {
        console.log(
          `✅ Loaded ${transformedDepts.length} departments from database`
        );
      }
      res.json({
        success: true,
        data: transformedDepts,
      });
    } catch (error) {
      console.error("Department fetch error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch departments",
      });
    }
  },

  // Get offices by department
  getOfficesByDepartment: async (req, res) => {
    try {
      const { departmentId } = req.params;

      const offices = await Office.findAll({
        where: {
          department_id: departmentId,
          is_active: true,
        },
        attributes: [
          "id",
          "name",
          "name_amharic",
          "office_number",
          "description",
        ],
        order: [["office_number", "ASC"]],
      });

      if (process.env.NODE_ENV === "development") {
        console.log(
          `✅ Loaded ${offices.length} offices for department ${departmentId} from database`
        );
      }
      res.json({
        success: true,
        data: offices,
      });
    } catch (error) {
      console.error("Office fetch error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch offices",
      });
    }
  },

  // Get employees by department (for rating)
  getEmployeesByDepartment: async (req, res) => {
    try {
      const { departmentId } = req.params;

      const employees = await Employee.findAll({
        where: {
          department_en: departmentId,
        },
        attributes: [
          "id",
          "first_name_en",
          "last_name_en",
          "position_en",
          "office_number",
        ],
        order: [["first_name_en", "ASC"]],
      });

      res.json({
        success: true,
        data: employees,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to fetch employees",
      });
    }
  },

  // Get all unique subcities
  getSubcities: async (req, res) => {
    try {
      const subcities = await Employee.findAll({
        attributes: ["subcity"],
        where: {
          subcity: { [Op.ne]: null },
        },
        group: ["subcity"],
        raw: true,
        order: [["subcity", "ASC"]],
      });

      const subcityList = subcities
        .map((item) => item.subcity)
        .filter((subcity) => subcity && subcity.trim() !== "");

      if (process.env.NODE_ENV === "development") {
        console.log(`Loaded ${subcityList.length} subcities from database`);
      }
      res.json({
        success: true,
        data: subcityList,
      });
    } catch (error) {
      console.error("Subcity fetch error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch subcities",
      });
    }
  },

  // =================================
  // PUBLIC COMPLAINTS
  // =================================

  // Submit a text complaint
  submitComplaint: [
    ...complaintValidation,
    async (req, res) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({
            success: false,
            message: "Validation failed",
            errors: errors.array(),
          });
        }

        const {
          complainant_name,
          phone_number,
          sub_city,
          kebele,
          complaint_description,
          complaint_date,
          department,
          office,
          desired_action,
        } = req.body;

        const trackingCode = generateTrackingCode();

        const complaint = await PublicComplaint.create({
          complainant_name,
          phone_number,
          sub_city,
          kebele,
          complaint_description,
          complaint_date: complaint_date || new Date(),
          department,
          office,
          desired_action,
          tracking_code: trackingCode,
          status: "pending",
        });

        console.log(
          `✅ Complaint created with ID: ${complaint.id}, tracking: ${trackingCode}`
        );
        res.status(201).json({
          success: true,
          message: "Complaint submitted successfully",
          data: {
            tracking_code: trackingCode,
            complaint_id: complaint.id,
            status: "pending",
          },
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: "Failed to submit complaint",
        });
      }
    },
  ],

  // Submit a voice complaint
  submitVoiceComplaint: [
    voiceUpload.single("voice_file"),
    ...complaintValidation.filter(
      (rule) =>
        !rule._validations?.some((v) => v.key === "complaint_description")
    ), // Remove description validation for voice
    body("complaint_description").optional(), // Make description optional for voice
    async (req, res) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({
            success: false,
            message: "Validation failed",
            errors: errors.array(),
          });
        }

        if (!req.file) {
          return res.status(400).json({
            success: false,
            message: "Voice file is required",
          });
        }

        const {
          complainant_name,
          phone_number,
          sub_city,
          kebele,
          complaint_description,
          department,
          office,
          desired_action,
        } = req.body;

        const trackingCode = generateTrackingCode();

        const complaint = await PublicComplaint.create({
          complainant_name,
          phone_number,
          sub_city,
          kebele,
          complaint_description:
            complaint_description ||
            "Voice complaint - please listen to audio file",
          complaint_date: new Date(),
          department,
          office,
          desired_action,
          tracking_code: trackingCode,
          voice_file_path: req.file.path,
          status: "pending",
        });

        res.status(201).json({
          success: true,
          message: "Voice complaint submitted successfully",
          data: {
            tracking_code: trackingCode,
            complaint_id: complaint.id,
            status: "pending",
            voice_file: req.file.filename,
          },
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: "Failed to submit voice complaint",
        });
      }
    },
  ],

  // Track complaint by tracking code or phone number
  trackComplaint: async (req, res) => {
    try {
      const { identifier } = req.params; // Can be tracking code or phone number

      let whereCondition;
      if (identifier.startsWith("TC-")) {
        whereCondition = { tracking_code: identifier };
      } else {
        whereCondition = { phone_number: identifier };
      }

      const complaints = await PublicComplaint.findAll({
        where: whereCondition,
        attributes: [
          "id",
          "tracking_code",
          "complainant_name",
          "complaint_description",
          "department",
          "office",
          "status",
          "response_text",
          "priority",
          "created_at",
          "resolved_at",
        ],
        order: [["created_at", "DESC"]],
      });

      if (complaints.length === 0) {
        return res.status(404).json({
          success: false,
          message: "No complaints found with this identifier",
        });
      }

      res.json({
        success: true,
        data: complaints,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to track complaint",
      });
    }
  },

  // =================================
  // PUBLIC RATINGS
  // =================================

  // Submit service rating
  submitRating: [
    ...ratingValidation,
    async (req, res) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({
            success: false,
            message: "Validation failed",
            errors: errors.array(),
          });
        }

        const {
          department,
          employee_id,
          overall_rating,
          courtesy_rating,
          timeliness_rating,
          knowledge_rating,
          comments,
          citizen_name,
          citizen_phone,
        } = req.body;

        // Generate tracking code for rating
        const trackingCode = generateTrackingCode();

        const rating = await PublicRating.create({
          citizen_name: citizen_name || "Anonymous",
          phone_number: citizen_phone || "N/A",
          department,
          service_type: "General Service Rating",
          employee_id: employee_id || null,
          service_rating: overall_rating,
          staff_behavior: courtesy_rating,
          facility_rating: timeliness_rating,
          waiting_time_rating: timeliness_rating,
          overall_satisfaction: overall_rating,
          comments: comments,
          tracking_code: trackingCode,
        });

        res.status(201).json({
          success: true,
          message: "Rating submitted successfully",
          data: {
            rating_id: rating.id,
            tracking_code: trackingCode,
            average_rating: (
              (overall_rating +
                courtesy_rating +
                timeliness_rating +
                knowledge_rating) /
              4
            ).toFixed(1),
          },
        });
      } catch (error) {
        console.error("Rating submission error:", error);
        res.status(500).json({
          success: false,
          message: "Failed to submit rating",
          error:
            process.env.NODE_ENV === "development" ? error.message : undefined,
        });
      }
    },
  ],

  // Get department ratings summary
  getDepartmentRatings: async (req, res) => {
    try {
      const { department } = req.params;

      const ratings = await PublicRating.findAll({
        where: { department },
        attributes: [
          "overall_rating",
          "courtesy_rating",
          "timeliness_rating",
          "knowledge_rating",
          "created_at",
        ],
        order: [["created_at", "DESC"]],
        limit: 100, // Last 100 ratings
      });

      if (ratings.length === 0) {
        return res.json({
          success: true,
          data: {
            department,
            total_ratings: 0,
            averages: {
              overall: 0,
              courtesy: 0,
              timeliness: 0,
              knowledge: 0,
            },
          },
        });
      }

      const totals = ratings.reduce(
        (acc, rating) => {
          acc.overall += rating.overall_rating;
          acc.courtesy += rating.courtesy_rating;
          acc.timeliness += rating.timeliness_rating;
          acc.knowledge += rating.knowledge_rating;
          return acc;
        },
        { overall: 0, courtesy: 0, timeliness: 0, knowledge: 0 }
      );

      const count = ratings.length;

      res.json({
        success: true,
        data: {
          department,
          total_ratings: count,
          averages: {
            overall: (totals.overall / count).toFixed(1),
            courtesy: (totals.courtesy / count).toFixed(1),
            timeliness: (totals.timeliness / count).toFixed(1),
            knowledge: (totals.knowledge / count).toFixed(1),
          },
        },
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to fetch department ratings",
      });
    }
  },

  // =================================
  // PUBLIC FEEDBACK
  // =================================

  // Submit general feedback
  submitFeedback: [
    ...feedbackValidation,
    async (req, res) => {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({
            success: false,
            message: "Validation failed",
            errors: errors.array(),
          });
        }

        const {
          full_name,
          email,
          phone_number,
          service_type,
          feedback_type,
          message,
        } = req.body;

        const trackingCode = generateTrackingCode();

        const feedback = await PublicFeedback.create({
          citizen_name: full_name,
          email: email,
          phone_number: phone_number || "N/A",
          department: service_type || "General",
          feedback_type:
            feedback_type === "complaint" ? "concern" : feedback_type,
          subject: `${feedback_type} regarding ${
            service_type || "General Services"
          }`,
          feedback_text: message,
          tracking_code: trackingCode,
          status: "pending",
        });

        res.status(201).json({
          success: true,
          message: "Feedback submitted successfully",
          data: {
            reference_number: trackingCode,
            feedback_id: feedback.id,
            status: "pending",
          },
        });
      } catch (error) {
        console.error("Feedback submission error:", error);
        res.status(500).json({
          success: false,
          message: "Failed to submit feedback",
          error:
            process.env.NODE_ENV === "development" ? error.message : undefined,
        });
      }
    },
  ],

  // Check feedback status
  checkFeedbackStatus: async (req, res) => {
    try {
      const { referenceNumber } = req.params;

      const feedback = await PublicFeedback.findOne({
        where: { reference_number: referenceNumber },
        attributes: [
          "id",
          "reference_number",
          "full_name",
          "service_type",
          "feedback_type",
          "message",
          "status",
          "admin_response",
          "created_at",
          "responded_at",
        ],
      });

      if (!feedback) {
        return res.status(404).json({
          success: false,
          message: "Feedback not found with this reference number",
        });
      }

      res.json({
        success: true,
        data: feedback,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to check feedback status",
      });
    }
  },

  // =================================
  // TEAM/STAFF INFORMATION
  // =================================

  // Get team members (public directory)
  getTeamMembers: async (req, res) => {
    try {
      const { search, department, limit = 20, offset = 0 } = req.query;

      let whereCondition = {};

      if (search) {
        whereCondition[Op.or] = [
          { first_name_en: { [Op.like]: `%${search}%` } },
          { last_name_en: { [Op.like]: `%${search}%` } },
          { position_en: { [Op.like]: `%${search}%` } },
          { office_number: { [Op.like]: `%${search}%` } },
        ];
      }

      if (department) {
        whereCondition.department_en = department;
      }

      const employees = await Employee.findAndCountAll({
        where: whereCondition,
        attributes: [
          "id",
          "first_name_en",
          "last_name_en",
          "position_en",
          "department_en",
          "office_number",
          "section",
          "city",
          "subcity",
          "profile_picture",
        ],
        order: [["first_name_en", "ASC"]],
        limit: parseInt(limit),
        offset: parseInt(offset),
      });

      res.json({
        success: true,
        data: {
          employees: employees.rows,
          total: employees.count,
          page: Math.floor(offset / limit) + 1,
          total_pages: Math.ceil(employees.count / limit),
        },
      });
    } catch (error) {
      console.error("Team members fetch error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch team members",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  },

  // Get individual team member details
  getTeamMember: async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findOne({
        where: {
          id: employeeId,
        },
        attributes: [
          "id",
          "first_name_en",
          "last_name_en",
          "position_en",
          "department_en",
          "office_number",
          "section",
          "city",
          "subcity",
          "profile_picture",
        ],
      });

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Team member not found",
        });
      }

      // Get average ratings for this employee
      const ratings = await PublicRating.findAll({
        where: { employee_id: employeeId },
        attributes: [
          "overall_rating",
          "courtesy_rating",
          "timeliness_rating",
          "knowledge_rating",
        ],
      });

      let averageRatings = null;
      if (ratings.length > 0) {
        const totals = ratings.reduce(
          (acc, rating) => {
            acc.overall += rating.overall_rating;
            acc.courtesy += rating.courtesy_rating;
            acc.timeliness += rating.timeliness_rating;
            acc.knowledge += rating.knowledge_rating;
            return acc;
          },
          { overall: 0, courtesy: 0, timeliness: 0, knowledge: 0 }
        );

        averageRatings = {
          overall: (totals.overall / ratings.length).toFixed(1),
          courtesy: (totals.courtesy / ratings.length).toFixed(1),
          timeliness: (totals.timeliness / ratings.length).toFixed(1),
          knowledge: (totals.knowledge / ratings.length).toFixed(1),
          total_ratings: ratings.length,
        };
      }

      res.json({
        success: true,
        data: {
          ...employee.toJSON(),
          ratings: averageRatings,
        },
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to fetch team member details",
      });
    }
  },

  // Get all sectors
  getSectors: async (req, res) => {
    try {
      const sectors = await Sector.findAll({
        attributes: ["id", "name"],
        order: [["name", "ASC"]],
      });

      res.json({
        success: true,
        data: sectors,
      });
    } catch (error) {
      console.error("Sectors fetch error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch sectors",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  },

  // Get all divisions
  getDivisions: async (req, res) => {
    try {
      const divisions = await Division.findAll({
        attributes: ["id", "name", "sector_id"],
        include: [
          {
            model: Sector,
            as: "sector",
            attributes: ["id", "name"],
          },
        ],
        order: [["name", "ASC"]],
      });

      res.json({
        success: true,
        data: divisions,
      });
    } catch (error) {
      console.error("Divisions fetch error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch divisions",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  },

  // Get divisions by sector ID
  getDivisionsBySector: async (req, res) => {
    try {
      const { sectorId } = req.params;

      // Validate sector exists
      const sector = await Sector.findByPk(sectorId);
      if (!sector) {
        return res.status(404).json({
          success: false,
          message: "Sector not found",
        });
      }

      const divisions = await Division.findAll({
        where: {
          sector_id: sectorId,
        },
        attributes: ["id", "name", "sector_id"],
        include: [
          {
            model: Sector,
            as: "sector",
            attributes: ["id", "name"],
          },
        ],
        order: [["name", "ASC"]],
      });

      res.json({
        success: true,
        data: divisions,
        sector: {
          id: sector.id,
          name: sector.name,
        },
      });
    } catch (error) {
      console.error("Divisions by sector fetch error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch divisions for sector",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  },
};

module.exports = publicController;
