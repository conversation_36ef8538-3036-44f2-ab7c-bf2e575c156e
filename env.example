# =====================================
# OFFICE MANAGEMENT SYSTEM - ENVIRONMENT CONFIGURATION
# =====================================
# Copy this file to .env and update the values for your hosting server

# Application Configuration
NODE_ENV=production
PORT=4000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=office_management
DB_USER=your_database_username
DB_PASSWORD=your_database_password

# Security Configuration
JWT_SECRET=your_jwt_secret_key_here_make_it_very_long_and_secure

# CORS Configuration (Add your frontend domain)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Upload Configuration
MAX_FILE_SIZE=10mb
UPLOAD_PATH=./Uploads

# Email Configuration (Optional - for notifications)
EMAIL_HOST=smtp.yourdomain.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# Application URLs
FRONTEND_URL=https://yourdomain.com
API_BASE_URL=https://api.yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security Settings
SESSION_SECRET=your_session_secret_key

# Monitoring & Logging
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# Database Connection Pool
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# Rate Limiting
AUTH_RATE_LIMIT_MAX=5 