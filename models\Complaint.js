const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Complaint = sequelize.define(
  
  "Complaint",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    complainant_name: { type: DataTypes.STRING(50), allowNull: true },
    phone_number: { type: DataTypes.STRING(20), allowNull: false },
    section: { type: DataTypes.STRING(255), allowNull: false },
    kebele: { type: DataTypes.STRING(20), allowNull: true },
    province: { type: DataTypes.STRING(100), allowNull: true },
    department: { type: DataTypes.STRING(255), allowNull: true },
    employee_id: { type: DataTypes.INTEGER, allowNull: true },
    description_en: { type: DataTypes.TEXT, allowNull: true },
    description_am: { type: DataTypes.TEXT, allowNull: true },
    description_af: { type: DataTypes.TEXT, allowNull: true },
    desired_action_en: { type: DataTypes.TEXT, allowNull: true },
    desired_action_am: { type: DataTypes.TEXT, allowNull: true },
    desired_action_af: { type: DataTypes.TEXT, allowNull: true },
    response_en: { type: DataTypes.TEXT, allowNull: true },
    response_am: { type: DataTypes.TEXT, allowNull: true },
    response_af: { type: DataTypes.TEXT, allowNull: true },
    voice_file: { type: DataTypes.STRING(255), allowNull: true },
    tracking_code: {
      type: DataTypes.STRING(36),
      allowNull: false,
      unique: true,
    },
    status: {
      type: DataTypes.ENUM("pending", "resolved"),
      defaultValue: "pending",
    },
    created_at: { type: DataTypes.DATE, allowNull: true },
  },
  { tableName: "complaints", timestamps: false }
);

module.exports = Complaint;
