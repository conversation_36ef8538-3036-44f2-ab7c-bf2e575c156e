const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const PublicRating = sequelize.define(
  "PublicRating",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },

    // Contact Information
    citizen_name: { type: DataTypes.STRING(100), allowNull: true },
    phone_number: { type: DataTypes.STRING(15), allowNull: false },

    // Service Information
    department: { type: DataTypes.STRING(100), allowNull: false },
    service_type: { type: DataTypes.STRING(100), allowNull: false },
    visit_date: { type: DataTypes.DATE, allowNull: true },
    employee_id: { type: DataTypes.INTEGER, allowNull: true },

    // Ratings (1-5 scale)
    service_rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 5 },
    },
    staff_behavior: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 5 },
    },
    facility_rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 5 },
    },
    waiting_time_rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 5 },
    },
    overall_satisfaction: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 5 },
    },
    accessibility_rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: { min: 1, max: 5 },
    },
    digital_services_rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: { min: 1, max: 5 },
    },

    // Additional feedback
    comments: { type: DataTypes.TEXT, allowNull: true },
    recommendations: { type: DataTypes.TEXT, allowNull: true },
    would_recommend: { type: DataTypes.BOOLEAN, allowNull: true },
    anonymous: { type: DataTypes.BOOLEAN, defaultValue: false },

    // System Generated
    tracking_code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
    },
  },
  {
    tableName: "public_ratings",
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ["tracking_code"] },
      { fields: ["department"] },
      { fields: ["employee_id"] },
      { fields: ["overall_satisfaction"] },
      { fields: ["service_rating"] },
      { fields: ["phone_number"] },
      { fields: ["created_at"] },
      { fields: ["department", "overall_satisfaction"] },
    ],
  }
);

module.exports = PublicRating;
