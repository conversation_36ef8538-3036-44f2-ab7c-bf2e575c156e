const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const PublicComplaint = sequelize.define(
  "PublicComplaint",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },

    // Complainant Information
    complainant_name: { type: DataTypes.STRING(100), allowNull: false },
    phone_number: { type: DataTypes.STRING(15), allowNull: false },
    sub_city: { type: DataTypes.STRING(50), allowNull: false },
    kebele: { type: DataTypes.STRING(20), allowNull: false },

    // Complaint Details
    complaint_description: { type: DataTypes.TEXT, allowNull: false },
    complaint_date: { type: DataTypes.DATE, allowNull: false },
    department: { type: DataTypes.STRING(100), allowNull: false },
    office: { type: DataTypes.STRING(100), allowNull: false },
    desired_action: { type: DataTypes.TEXT, allowNull: false },

    // System Generated
    tracking_code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
    },
    status: {
      type: DataTypes.ENUM("pending", "in_progress", "resolved", "closed"),
      defaultValue: "pending",
    },

    // Voice complaint support
    voice_file_path: { type: DataTypes.STRING, allowNull: true },

    // Admin response
    response_text: { type: DataTypes.TEXT, allowNull: true },
    resolved_by: { type: DataTypes.INTEGER, allowNull: true }, // Admin ID
    resolved_at: { type: DataTypes.DATE, allowNull: true },

    // Priority and internal notes
    priority: {
      type: DataTypes.ENUM("low", "medium", "high", "urgent"),
      defaultValue: "medium",
    },
    internal_notes: { type: DataTypes.TEXT, allowNull: true },
  },
  {
    tableName: "public_complaints",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      { fields: ["tracking_code"] },
      { fields: ["phone_number"] },
      { fields: ["status"] },
      { fields: ["department"] },
      { fields: ["created_at"] },
      { fields: ["status", "department"] },
    ],
  }
);

module.exports = PublicComplaint;
