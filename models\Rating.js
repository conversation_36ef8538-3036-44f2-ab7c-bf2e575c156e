const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Rating = sequelize.define(
  "Rating",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    employee_id: { type: DataTypes.INTEGER, allowNull: false },
    phone_number: { type: DataTypes.STRING(20), allowNull: false },
    courtesy: { type: DataTypes.INTEGER, allowNull: false },
    timeliness: { type: DataTypes.INTEGER, allowNull: false },
    knowledge: { type: DataTypes.INTEGER, allowNull: false },
    overall_experience: { type: DataTypes.TEXT, allowNull: true },
    suggestions: { type: DataTypes.TEXT, allowNull: true },
    created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
  },
  { tableName: "ratings", timestamps: false }
);

module.exports = Rating;
