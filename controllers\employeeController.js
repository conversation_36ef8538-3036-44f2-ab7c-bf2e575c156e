const { Employee } = require("../models");
const { validateLanguage, addProfilePictureUrl } = require("../utils/helpers");
const { buildLocationFilter } = require("../middleware/auth");

// Get all employees (public endpoint)
const getEmployees = async (req, res) => {
  try {
    const { lang = "en" } = req.query;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const employees = await Employee.findAll({
      attributes: [
        "id",
        [`first_name_${lang}`, "first_name"],
        [`middle_name_${lang}`, "middle_name"],
        [`last_name_${lang}`, "last_name"],
        "office_number",
        "floor_number",
        [`position_${lang}`, "position"],
        [`department_${lang}`, "department"],
        "section",
        "city",
        "subcity",
        "profile_picture",
        // Organizational hierarchy fields
        "sector_id",
        "division_id",
        "department_id",
        "team_id",
      ],
      include: [
        {
          model: require("../models").Sector,
          as: "sector",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Division,
          as: "division",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Department,
          as: "employeeDepartment",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Team,
          as: "team",
          attributes: ["id", "name"],
          required: false,
        },
      ],
      order: [["created_at", "DESC"]],
    });

    const employeesWithUrls = employees.map(addProfilePictureUrl);

    // Add hierarchy information to response
    const employeesWithHierarchy = employeesWithUrls.map(employee => ({
      ...employee,
      hierarchy: {
        sector: employee.sector || null,
        division: employee.division || null,
        department: employee.employeeDepartment || null,
        team: employee.team || null,
      }
    }));

    res.json(employeesWithHierarchy);
  } catch (error) {
    console.error("Error fetching employees:", error);
    res
      .status(500)
      .json({ message: "Error fetching employees", error: error.message });
  }
};

// Get employees for admin (with role-based filtering)
const getEmployeesAdmin = async (req, res) => {
  try {
    const { lang = "en", city, subcity } = req.query;
    const admin = req.user;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    let where = {};

    // Apply role-based filtering
    if (admin.role === "Admin") {
      // Department admin: filter by department
      where[`department_${lang}`] = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      // SubCity admin: filter by their specific subcity
      where.city = admin.city;
      where.subcity = admin.subcity;
    } else if (admin.role === "CityAdmin") {
      // City admin: filter by their city (all subcities)
      where.city = admin.city;
    }
    // SuperAdmin sees all employees (no filter)

    // SuperAdmin can also filter by specific city/subcity via query params
    if (admin.role === "SuperAdmin") {
      if (city) where.city = city;
      if (subcity) where.subcity = subcity;
    }

    const employees = await Employee.findAll({
      where,
      attributes: [
        "id",
        [`first_name_${lang}`, "first_name"],
        [`middle_name_${lang}`, "middle_name"],
        [`last_name_${lang}`, "last_name"],
        "office_number",
        "floor_number",
        [`position_${lang}`, "position"],
        [`department_${lang}`, "department"],
        "section",
        "city",
        "subcity",
        "profile_picture",
        "created_at",
        // Organizational hierarchy fields
        "sector_id",
        "division_id",
        "department_id",
        "team_id",
      ],
      include: [
        {
          model: require("../models").Sector,
          as: "sector",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Division,
          as: "division",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Department,
          as: "employeeDepartment",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Team,
          as: "team",
          attributes: ["id", "name"],
          required: false,
        },
      ],
      order: [["created_at", "DESC"]],
    });

    const employeesWithUrls = employees.map(addProfilePictureUrl);

    // Add hierarchy information to response
    const employeesWithHierarchy = employeesWithUrls.map(employee => ({
      ...employee,
      hierarchy: {
        sector: employee.sector || null,
        division: employee.division || null,
        department: employee.employeeDepartment || null,
        team: employee.team || null,
      }
    }));

    res.json(employeesWithHierarchy);
  } catch (error) {
    console.error("Error fetching employees:", error);
    res
      .status(500)
      .json({ message: "Error fetching employees", error: error.message });
  }
};

// Create employee
const createEmployee = async (req, res) => {
  try {
    const {
      // Handle both single and multilingual field names
      first_name,
      first_name_en,
      first_name_am,
      first_name_af,
      middle_name,
      middle_name_en,
      middle_name_am,
      middle_name_af,
      last_name,
      last_name_en,
      last_name_am,
      last_name_af,
      position,
      position_en,
      position_am,
      position_af,
      department,
      department_en,
      department_am,
      department_af,
      office_number,
      floor_number,
      section,
      city,
      subcity,
      lang = "en",
      // Organizational hierarchy fields
      sector_id,
      division_id,
      department_id,
      team_id,
    } = req.body;
    const admin = req.user;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    // Extract the actual field values - prioritize multilingual fields if available
    const actualFirstName = first_name_en || first_name;
    const actualLastName = last_name_en || last_name;
    const actualPosition = position_en || position;
    const actualDepartment = department_en || department;

    if (!actualFirstName || !actualLastName || !city || !subcity) {
      return res.status(400).json({
        message: "First name, last name, city, and subcity are required",
      });
    }

    // Role-based access control for creating employees
    if (admin.role === "Admin" && actualDepartment !== admin.department) {
      return res
        .status(403)
        .json({ message: "Cannot create employee for another department" });
    }
    if (admin.role === "SubCityAdmin") {
      if (city !== admin.city || subcity !== admin.subcity) {
        return res
          .status(403)
          .json({ message: "Cannot create employee for another subcity" });
      }
    }
    if (admin.role === "CityAdmin" && city !== admin.city) {
      return res
        .status(403)
        .json({ message: "Cannot create employee for another city" });
    }

    const employeeData = {
      // Store multilingual data if available, otherwise use single language
      first_name_en: first_name_en || actualFirstName,
      first_name_am: first_name_am || actualFirstName,
      first_name_af: first_name_af || actualFirstName,
      middle_name_en: middle_name_en || middle_name || "",
      middle_name_am: middle_name_am || middle_name || "",
      middle_name_af: middle_name_af || middle_name || "",
      last_name_en: last_name_en || actualLastName,
      last_name_am: last_name_am || actualLastName,
      last_name_af: last_name_af || actualLastName,
      office_number,
      floor_number,
      position_en: position_en || actualPosition || "",
      position_am: position_am || actualPosition || "",
      position_af: position_af || actualPosition || "",
      department_en: department_en || actualDepartment || "",
      department_am: department_am || actualDepartment || "",
      department_af: department_af || actualDepartment || "",
      section: section || subcity, // Use subcity as section for backward compatibility
      city,
      subcity,
      // Organizational hierarchy fields
      sector_id: sector_id ? parseInt(sector_id) : null,
      division_id: division_id ? parseInt(division_id) : null,
      department_id: department_id ? parseInt(department_id) : null,
      team_id: team_id ? parseInt(team_id) : null,
    };

    if (req.file) {
      employeeData.profile_picture = req.file.filename;
    }

    const employee = await Employee.create(employeeData);

    // Fetch the created employee with hierarchy information
    const employeeWithHierarchy = await Employee.findByPk(employee.id, {
      include: [
        {
          model: require("../models").Sector,
          as: "sector",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Division,
          as: "division",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Department,
          as: "employeeDepartment",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Team,
          as: "team",
          attributes: ["id", "name"],
          required: false,
        },
      ],
    });

    res.status(201).json({
      message: "Employee created successfully",
      employee: {
        id: employee.id,
        first_name: employee[`first_name_${lang}`],
        middle_name: employee[`middle_name_${lang}`],
        last_name: employee[`last_name_${lang}`],
        office_number: employee.office_number,
        floor_number: employee.floor_number,
        position: employee[`position_${lang}`],
        department: employee[`department_${lang}`],
        section: employee.section,
        city: employee.city,
        subcity: employee.subcity,
        profile_picture: employee.profile_picture
          ? `/Uploads/profile_pictures/${employee.profile_picture}`
          : null,
        // Organizational hierarchy information
        sector_id: employee.sector_id,
        division_id: employee.division_id,
        department_id: employee.department_id,
        team_id: employee.team_id,
        hierarchy: {
          sector: employeeWithHierarchy?.sector || null,
          division: employeeWithHierarchy?.division || null,
          department: employeeWithHierarchy?.employeeDepartment || null,
          team: employeeWithHierarchy?.team || null,
        },
      },
    });
  } catch (error) {
    console.error("Error creating employee:", error);
    res
      .status(500)
      .json({ message: "Error creating employee", error: error.message });
  }
};

// Update employee
const updateEmployee = async (req, res) => {
  try {
    const {
      first_name,
      first_name_en,
      first_name_am,
      first_name_af,
      middle_name,
      middle_name_en,
      middle_name_am,
      middle_name_af,
      last_name,
      last_name_en,
      last_name_am,
      last_name_af,
      office_number,
      floor_number,
      position,
      position_en,
      position_am,
      position_af,
      department,
      department_en,
      department_am,
      department_af,
      section,
      city,
      subcity,
      lang = "en",
      // Organizational hierarchy fields
      sector_id,
      division_id,
      department_id,
      team_id,
    } = req.body;
    const { id } = req.params;
    const admin = req.user;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    // Check if admin has permission to edit this employee
    const employee = await Employee.findOne({ where: { id } });
    if (!employee) {
      return res.status(404).json({ message: "Employee not found" });
    }

    if (admin.role === "SubCityAdmin") {
      if (employee.city !== admin.city || employee.subcity !== admin.subcity) {
        return res
          .status(403)
          .json({ message: "Cannot edit employee from another subcity" });
      }
    } else if (admin.role === "CityAdmin") {
      if (employee.city !== admin.city) {
        return res
          .status(403)
          .json({ message: "Cannot edit employee from another city" });
      }
    }

    const updateData = {
      [`first_name_${lang}`]: first_name,
      [`middle_name_${lang}`]: middle_name,
      [`last_name_${lang}`]: last_name,
      office_number: office_number,
      floor_number: floor_number,
      [`position_${lang}`]: position,
      [`department_${lang}`]: department,
      section: section || subcity,
      city,
      subcity,
      // Organizational hierarchy fields
      sector_id: sector_id ? parseInt(sector_id) : null,
      division_id: division_id ? parseInt(division_id) : null,
      department_id: department_id ? parseInt(department_id) : null,
      team_id: team_id ? parseInt(team_id) : null,
    };

    if (req.file) {
      updateData.profile_picture = req.file.filename;
    }

    await Employee.update(updateData, { where: { id } });

    const updatedEmployee = await Employee.findByPk(id, {
      include: [
        {
          model: require("../models").Sector,
          as: "sector",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Division,
          as: "division",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Department,
          as: "employeeDepartment",
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: require("../models").Team,
          as: "team",
          attributes: ["id", "name"],
          required: false,
        },
      ],
    });

    res.json({
      message: "Employee updated successfully",
      employee: {
        id: updatedEmployee.id,
        first_name: updatedEmployee[`first_name_${lang}`],
        middle_name: updatedEmployee[`middle_name_${lang}`],
        last_name: updatedEmployee[`last_name_${lang}`],
        office_number: updatedEmployee.office_number,
        floor_number: updatedEmployee.floor_number,
        position: updatedEmployee[`position_${lang}`],
        department: updatedEmployee[`department_${lang}`],
        section: updatedEmployee.section,
        city: updatedEmployee.city,
        subcity: updatedEmployee.subcity,
        profile_picture: updatedEmployee.profile_picture
          ? `/Uploads/profile_pictures/${updatedEmployee.profile_picture}`
          : null,
        // Organizational hierarchy information
        sector_id: updatedEmployee.sector_id,
        division_id: updatedEmployee.division_id,
        department_id: updatedEmployee.department_id,
        team_id: updatedEmployee.team_id,
        hierarchy: {
          sector: updatedEmployee.sector || null,
          division: updatedEmployee.division || null,
          department: updatedEmployee.employeeDepartment || null,
          team: updatedEmployee.team || null,
        },
      },
    });
  } catch (error) {
    console.error("Error updating employee:", error);
    res
      .status(500)
      .json({ message: "Error updating employee", error: error.message });
  }
};

// Delete employee
const deleteEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    const admin = req.user;

    // Check if admin has permission to delete this employee
    const employee = await Employee.findOne({ where: { id } });
    if (!employee) {
      return res.status(404).json({ message: "Employee not found" });
    }

    if (admin.role === "SubCityAdmin") {
      if (employee.city !== admin.city || employee.subcity !== admin.subcity) {
        return res
          .status(403)
          .json({ message: "Cannot delete employee from another subcity" });
      }
    } else if (admin.role === "CityAdmin") {
      if (employee.city !== admin.city) {
        return res
          .status(403)
          .json({ message: "Cannot delete employee from another city" });
      }
    }

    await Employee.destroy({ where: { id } });

    res.json({ message: "Employee deleted successfully" });
  } catch (error) {
    console.error("Error deleting employee:", error);
    res
      .status(500)
      .json({ message: "Error deleting employee", error: error.message });
  }
};

module.exports = {
  getEmployees,
  getEmployeesAdmin,
  createEmployee,
  updateEmployee,
  deleteEmployee,
};
