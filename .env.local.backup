# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456789@abc
DB_NAME=office

# Security Settings
JWT_SECRET=T63eMfNwEcOhAs/xhQCXsm7A37bey3cVlMESC6luib9gCtWHFbSdb1irty2xu6MRZe7IuGEHx2rQdPetnOAhmxR8+N7wGOpR/fdds7Sqvo8xGppUrMo0LXzkHg7R7lQSTAHDRvseCfb62ynqPeFWJ70Jk0LCJSg+gh/Ald208hAtJfN+1rGdR22wS4XFI/4gsXjaNadT5F2syMFYfiwuHlfzduDoFVz6ZZRIL/YTeto4H3h2N5blL08/Ml8JmKlrnPFAogwxlP9H63MJO7tnlqLJoSNPEtJqw9/NmN9uCNsFgnKow+oyQREPhholsSfU
rmmwbZR5OxCLHuDaSCK9MA==
NODE_ENV=development
LOG_LEVEL=debug
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
# SESSION_SECRET=your_session_secret_key

# Server Configuration
PORT=4000

# File Upload Settings
MAX_FILE_SIZE=52428800
UPLOAD_PATH=./Uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5

# Database Connection Pool
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# Monitoring & Logging
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true 