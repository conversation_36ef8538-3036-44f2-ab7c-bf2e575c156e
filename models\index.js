const Admin = require("./Admin");
const Employee = require("./Employee");
const Complaint = require("./Complaint");
const Rating = require("./Rating");
const Feedback = require("./Feedback");
const PasswordReset = require("./PasswordReset");

// Public citizen-facing models
const PublicComplaint = require("./PublicComplaint");
const PublicRating = require("./PublicRating");
const PublicFeedback = require("./PublicFeedback");
const Department = require("./Department");
const Office = require("./Office");
const Sector = require("./Sector");
const Division = require("./Division");
const Team = require("./Team");

// Define relationships
Complaint.belongsTo(Employee, { foreignKey: "employee_id" });
Rating.belongsTo(Employee, { foreignKey: "employee_id" });
Feedback.belongsTo(Employee, { foreignKey: "employee_id" });
PasswordReset.belongsTo(Admin, { foreignKey: "admin_id" });
Admin.hasMany(PasswordReset, { foreignKey: "admin_id" });

// Public models relationships
Department.hasMany(Office, { foreignKey: "department_id", as: "offices" });
Office.belongsTo(Department, { foreignKey: "department_id", as: "department" });

// Sector and Division relationships
Sector.hasMany(Division, { foreignKey: "sector_id", as: "divisions" });
Division.belongsTo(Sector, { foreignKey: "sector_id", as: "sector" });

// Division and Department relationships
Division.hasMany(Department, { foreignKey: "division_id", as: "departments" });
Department.belongsTo(Division, { foreignKey: "division_id", as: "division" });

// Department and Team relationships
Department.hasMany(Team, { foreignKey: "department_id", as: "teams" });
Team.belongsTo(Department, { foreignKey: "department_id", as: "department" });

PublicComplaint.belongsTo(Admin, { foreignKey: "resolved_by", as: "resolver" });
PublicRating.belongsTo(Employee, { foreignKey: "employee_id", as: "employee" });
PublicFeedback.belongsTo(Admin, {
  foreignKey: "responded_by",
  as: "responder",
});

module.exports = {
  Admin,
  Employee,
  Complaint,
  Rating,
  Feedback,
  PasswordReset,
  ActivityLog,
  SystemSetting,

  // Public models
  PublicComplaint,
  PublicRating,
  PublicFeedback,
  Department,
  Office,
  Sector,
  Division,
  Team,
};
