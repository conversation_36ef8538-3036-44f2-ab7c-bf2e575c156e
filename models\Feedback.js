const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Feedback = sequelize.define(
  "Feedback",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    phone_number: { type: DataTypes.STRING(20), allowNull: false },
    section: { type: DataTypes.STRING, allowNull: false },
    department: { type: DataTypes.STRING, allowNull: true },
    employee_id: { type: DataTypes.INTEGER, allowNull: true },
    comment_en: { type: DataTypes.TEXT, allowNull: true },
    comment_am: { type: DataTypes.TEXT, allowNull: true },
    comment_af: { type: DataTypes.TEXT, allowNull: true },
    rating: { type: DataTypes.INTEGER, allowNull: true },
    created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
  },
  { tableName: "feedbacks", timestamps: false }
);

module.exports = Feedback;
