const { Feedback, Employee } = require("../models");
const {
  validateLanguage,
  getSentiment,
  addProfilePictureUrl,
} = require("../utils/helpers");
const { Op } = require("sequelize");

// Create feedback
const createFeedback = async (req, res) => {
  try {
    const {
      phone_number,
      section,
      department,
      employee_id,
      comment,
      rating,
      lang = "en",
    } = req.body;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }
    if (!phone_number || !section || !comment) {
      return res
        .status(400)
        .json({ message: "Phone number, section, and comment are required" });
    }
    if (rating && ![1, 2, 3, 4, 5].includes(Number(rating))) {
      return res
        .status(400)
        .json({ message: "Rating must be between 1 and 5" });
    }

    const feedbackData = {
      phone_number,
      section,
      department,
      employee_id: employee_id || null,
      rating: rating || null,
      [`comment_${lang}`]: comment,
    };

    const feedback = await Feedback.create(feedbackData);

    let employeeDetails = null;
    if (employee_id) {
      const employee = await Employee.findByPk(employee_id, {
        attributes: [
          "id",
          [`first_name_${lang}`, "first_name"],
          [`middle_name_${lang}`, "middle_name"],
          [`last_name_${lang}`, "last_name"],
          "office_number",
          "floor_number",
          [`position_${lang}`, "position"],
          [`department_${lang}`, "department"],
          "section",
          "profile_picture",
        ],
      });
      employeeDetails = employee ? addProfilePictureUrl(employee) : null;
    }

    res.status(201).json({
      message: "Feedback submitted successfully",
      feedback: {
        id: feedback.id,
        phone_number: feedback.phone_number,
        section: feedback.section,
        department: feedback.department,
        employee_id: feedback.employee_id,
        comment: feedback[`comment_${lang}`],
        rating: feedback.rating,
        sentiment: getSentiment(feedback.rating),
        created_at: feedback.created_at,
        employee: employeeDetails,
      },
    });
  } catch (error) {
    console.error("Error submitting feedback:", error);
    res
      .status(500)
      .json({ message: "Error submitting feedback", error: error.message });
  }
};

// Get feedback for admin
const getFeedbackAdmin = async (req, res) => {
  try {
    const { lang = "en", date_from, date_to, section, department } = req.query;
    const admin = req.user;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const where = {};
    if (admin.role === "Admin") {
      where.department = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      where.section = admin.section;
    } // SuperAdmin: no filters
    if (section && admin.role === "SuperAdmin") where.section = section;
    if (department && admin.role === "SuperAdmin")
      where.department = department;
    if (date_from && date_to) {
      where.created_at = {
        [Op.between]: [new Date(date_from), new Date(date_to)],
      };
    }

    const feedback = await Feedback.findAll({
      where,
      attributes: [
        "id",
        "phone_number",
        "section",
        "department",
        "employee_id",
        [`comment_${lang}`, "comment"],
        "rating",
        "created_at",
      ],
      include: [
        {
          model: Employee,
          attributes: [
            "id",
            [`first_name_${lang}`, "first_name"],
            [`middle_name_${lang}`, "middle_name"],
            [`last_name_${lang}`, "last_name"],
            "office_number",
            "floor_number",
            [`position_${lang}`, "position"],
            [`department_${lang}`, "department"],
            "section",
            "profile_picture",
          ],
        },
      ],
      order: [["created_at", "DESC"]],
    });

    const feedbackWithUrls = feedback.map((fb) => ({
      ...fb.get({ plain: true }),
      sentiment: getSentiment(fb.rating),
      employee: fb.Employee ? addProfilePictureUrl(fb.Employee) : null,
    }));

    res.json(feedbackWithUrls);
  } catch (error) {
    console.error("Error fetching feedback:", error);
    res
      .status(500)
      .json({ message: "Error fetching feedback", error: error.message });
  }
};

module.exports = {
  createFeedback,
  getFeedbackAdmin,
};
