# Node modules (will be installed in container)
node_modules/
npm-debug.log*

# Git
.git/
.gitignore

# Documentation
*.md

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Logs
logs/
*.log

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file (use docker-compose env instead)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker files (not needed in container)
Dockerfile*
docker-compose*.yml

# Temporary files
tmp/
temp/ 