const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Division = sequelize.define(
  "Division",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    name: { type: DataTypes.STRING(100), allowNull: false },
    sector_id: { 
      type: DataTypes.INTEGER, 
      allowNull: false,
      references: {
        model: "sectors",
        key: "id",
      },
    },
    created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
  },
  { 
    tableName: "divisions", 
    timestamps: false,
    indexes: [
      { fields: ["sector_id"] },
      { fields: ["name"] },
    ],
  }
);

module.exports = Division;
