# Office Management System - Production Configuration

# Environment
NODE_ENV=production

# Server Configuration
PORT=4000
ALLOWED_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=office_management
DB_USER=office_user
DB_PASSWORD=your_secure_password

# Security
JWT_SECRET=your_super_secure_jwt_secret_key_here_min_32_chars
ENCRYPTION_KEY=your_32_character_encryption_key

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# File Upload Limits
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,mp3,wav,m4a,ogg,webm

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100
SUBMISSION_RATE_LIMIT=10

# Logging
LOG_LEVEL=error
LOG_FILE=logs/production.log

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090 