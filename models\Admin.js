const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Admin = sequelize.define(
  "Admin",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    username: { type: DataTypes.STRING, allowNull: false, unique: true },
    password: { type: DataTypes.STRING, allowNull: false },
    role: {
      type: DataTypes.ENUM("SuperAdmin", "SubCityAdmin", "Admin"),
      allowNull: false,
    },
    city: { type: DataTypes.STRING(100), allowNull: true },
    subcity: { type: DataTypes.STRING(100), allowNull: true },
    section: { type: DataTypes.STRING, allowNull: true },
    department: { type: DataTypes.STRING, allowNull: true },
    profile_picture: { type: DataTypes.STRING, allowNull: true },
    created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
  },
  { tableName: "admins", timestamps: false }
);

module.exports = Admin;
