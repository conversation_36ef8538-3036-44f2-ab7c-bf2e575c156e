const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const PublicFeedback = sequelize.define(
  "PublicFeedback",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },

    // Contact Information
    citizen_name: { type: DataTypes.STRING(100), allowNull: true },
    phone_number: { type: DataTypes.STRING(15), allowNull: false },
    email: { type: DataTypes.STRING(100), allowNull: true },

    // Service Information
    department: { type: DataTypes.STRING(100), allowNull: false },
    office_location: { type: DataTypes.STRING(200), allowNull: true },

    // Feedback Details
    feedback_type: {
      type: DataTypes.ENUM("suggestion", "compliment", "concern", "general"),
      allowNull: false,
    },
    subject: { type: DataTypes.STRING(200), allowNull: false },
    feedback_text: { type: DataTypes.TEXT, allowNull: false },
    service_received: { type: DataTypes.STRING(200), allowNull: true },
    improvement_suggestions: { type: DataTypes.TEXT, allowNull: true },

    // Contact Preferences
    contact_preference: {
      type: DataTypes.ENUM("sms", "email", "none"),
      defaultValue: "none",
    },

    // System Generated
    tracking_code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
    },
    status: {
      type: DataTypes.ENUM("pending", "reviewed", "responded"),
      defaultValue: "pending",
    },

    // Admin response
    response_text: { type: DataTypes.TEXT, allowNull: true },
    responded_by: { type: DataTypes.INTEGER, allowNull: true }, // Admin ID
    responded_at: { type: DataTypes.DATE, allowNull: true },
  },
  {
    tableName: "public_feedback",
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ["tracking_code"] },
      { fields: ["email"] },
      { fields: ["phone_number"] },
      { fields: ["feedback_type"] },
      { fields: ["status"] },
      { fields: ["department"] },
      { fields: ["created_at"] },
    ],
  }
);

module.exports = PublicFeedback;
