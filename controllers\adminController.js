const {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>wordReset,
  PublicComplaint,
  PublicFeedback,
  PublicRating,
  Department,
  Office,
} = require("../models");
const { validateLanguage, getSentiment } = require("../utils/helpers");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { Op } = require("sequelize");
const sequelize = require("../config/database");

// Admin login
const adminLogin = async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res
        .status(400)
        .json({ message: "Username and password are required" });
    }

    const admin = await Admin.findOne({ where: { username } });
    if (!admin) {
      return res.status(401).json({ message: "Invalid credentials" });
    }

    const isValidPassword = await bcrypt.compare(password, admin.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: "Invalid credentials" });
    }

    const token = jwt.sign(
      {
        id: admin.id,
        username: admin.username,
        role: admin.role,
        city: admin.city,
        subcity: admin.subcity,
        section: admin.subcity, // Keep for backward compatibility
        department: admin.department,
      },
      process.env.JWT_SECRET || "fallback_secret_key",
      { expiresIn: "24h" }
    );

    res.json({
      message: "Login successful",
      token,
      admin: {
        id: admin.id,
        username: admin.username,
        role: admin.role,
        city: admin.city,
        subcity: admin.subcity,
        section: admin.subcity, // Keep for backward compatibility
        department: admin.department,
        profile_picture: admin.profile_picture
          ? `/uploads/profile_pictures/${admin.profile_picture}`
          : null,
      },
    });
  } catch (error) {
    console.error("Error during admin login:", error);
    res
      .status(500)
      .json({ message: "Error during login", error: error.message });
  }
};

// Create admin
const createAdmin = async (req, res) => {
  try {
    const { username, password, role, city, subcity, department } = req.body;

    if (!username || !password || !role) {
      return res
        .status(400)
        .json({ message: "Username, password, and role are required" });
    }
    if (!["Admin", "SubCityAdmin"].includes(role)) {
      return res
        .status(400)
        .json({ message: "Invalid role. Use Admin or SubCityAdmin." });
    }
    if (role === "Admin" && !department) {
      return res
        .status(400)
        .json({ message: "Department is required for Admin role" });
    }
    if (role === "SubCityAdmin" && (!city || !subcity)) {
      return res.status(400).json({
        message: "City and subcity are required for SubCityAdmin role",
      });
    }

    const existingAdmin = await Admin.findOne({ where: { username } });
    if (existingAdmin) {
      return res.status(400).json({ message: "Username already exists" });
    }

    // Log admin creation (password omitted for security)
    console.log(`Creating ${role} with username: ${username}`);

    const hashedPassword = await bcrypt.hash(password, 10);

    const admin = await Admin.create({
      username,
      password: hashedPassword,
      role,
      city: role === "SubCityAdmin" ? city : null,
      subcity: role === "SubCityAdmin" ? subcity : null,
      department: role === "Admin" ? department : null,
      profile_picture: req.file ? req.file.filename : null,
    });

    res.status(201).json({
      message: `${role} created successfully`,
      admin: {
        id: admin.id,
        username: admin.username,
        role: admin.role,
        city: admin.city,
        subcity: admin.subcity,
        department: admin.department,
        profile_picture: admin.profile_picture
          ? `/uploads/profile_pictures/${admin.profile_picture}`
          : null,
      },
    });
  } catch (error) {
    console.error("Error creating admin:", error);
    res
      .status(500)
      .json({ message: "Error creating admin", error: error.message });
  }
};

// Update admin profile
const updateAdminProfile = async (req, res) => {
  try {
    const { username, password } = req.body;
    const admin = req.user;

    const updateData = {};
    if (username) updateData.username = username;
    if (password) updateData.password = await bcrypt.hash(password, 10);
    if (req.file) updateData.profile_picture = req.file.filename;

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({ message: "No fields provided for update" });
    }

    const [updated] = await Admin.update(updateData, {
      where: { id: admin.id },
    });

    if (!updated) {
      return res.status(404).json({ message: "Admin not found" });
    }

    const updatedAdmin = await Admin.findByPk(admin.id);

    res.json({
      message: "Profile updated successfully",
      admin: {
        id: updatedAdmin.id,
        username: updatedAdmin.username,
        role: updatedAdmin.role,
        section: updatedAdmin.section,
        department: updatedAdmin.department,
        profile_picture: updatedAdmin.profile_picture
          ? `/Uploads/profile_pictures/${updatedAdmin.profile_picture}`
          : null,
      },
    });
  } catch (error) {
    console.error("Error updating profile:", error);
    res
      .status(500)
      .json({ message: "Error updating profile", error: error.message });
  }
};

// Request password reset (generates secure token)
const requestPasswordReset = async (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.status(400).json({ message: "Username is required" });
    }

    const admin = await Admin.findOne({ where: { username } });
    if (!admin) {
      // Don't reveal if username exists for security
      return res.json({
        message: "If the username exists, a reset link will be sent.",
      });
    }

    // Generate cryptographically secure reset token
    const crypto = require("crypto");
    const resetToken = crypto.randomBytes(32).toString("hex");
    const hashedToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    // Token expires in 15 minutes
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000);

    // Invalidate any existing tokens for this admin
    await PasswordReset.update(
      { used: true },
      { where: { admin_id: admin.id, used: false } }
    );

    // Create new reset token record
    await PasswordReset.create({
      admin_id: admin.id,
      token: hashedToken,
      expires_at: expiresAt,
    });

    // In production, send email with resetToken (not hashedToken)
    console.log(`Password reset token for ${username}: ${resetToken}`);
    console.log(`Token expires at: ${expiresAt}`);

    res.json({
      message: "If the username exists, a reset link will be sent.",
      // Remove this in production - only for testing
      resetToken: resetToken,
    });
  } catch (error) {
    console.error("Error requesting password reset:", error);
    res.status(500).json({ message: "Error processing reset request" });
  }
};

// Reset password with token
const resetPassword = async (req, res) => {
  try {
    const { token, new_password } = req.body;

    if (!token || !new_password) {
      return res
        .status(400)
        .json({ message: "Token and new password are required" });
    }

    // Validate password strength
    if (new_password.length < 8) {
      return res
        .status(400)
        .json({ message: "Password must be at least 8 characters long" });
    }

    // Hash the provided token to compare with stored hash
    const crypto = require("crypto");
    const hashedToken = crypto.createHash("sha256").update(token).digest("hex");

    // Find valid, unused, non-expired token
    const resetRecord = await PasswordReset.findOne({
      where: {
        token: hashedToken,
        used: false,
        expires_at: { [require("sequelize").Op.gt]: new Date() },
      },
      include: [{ model: Admin }],
    });

    if (!resetRecord) {
      return res
        .status(400)
        .json({ message: "Invalid or expired reset token" });
    }

    // Update password
    const hashedPassword = await bcrypt.hash(new_password, 12); // Increased salt rounds
    await Admin.update(
      { password: hashedPassword },
      { where: { id: resetRecord.admin_id } }
    );

    // Mark token as used
    await PasswordReset.update(
      { used: true },
      { where: { id: resetRecord.id } }
    );

    // Log security event
    console.log(
      `Password reset completed for admin ID: ${resetRecord.admin_id}`
    );

    res.json({ message: "Password reset successfully" });
  } catch (error) {
    console.error("Error resetting password:", error);
    res.status(500).json({ message: "Error resetting password" });
  }
};

// Get statistics
const getStatistics = async (req, res) => {
  try {
    const admin = req.user;
    const where = {};
    if (admin.role === "Admin") {
      where.department = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      where.section = admin.section;
    } // SuperAdmin: no filters

    const [
      complaintCount,
      pendingComplaints,
      resolvedComplaints,
      employeeCount,
      adminCount,
      ratingCount,
      feedbackCount,
    ] = await Promise.all([
      Complaint.count({ where }),
      Complaint.count({ where: { ...where, status: "pending" } }),
      Complaint.count({ where: { ...where, status: "resolved" } }),
      Employee.count({
        where:
          admin.role === "Admin"
            ? { [`department_en`]: admin.department }
            : admin.role === "SubCityAdmin"
            ? { section: admin.section }
            : {},
      }),
      Admin.count(),
      Rating.count({
        where:
          admin.role === "Admin"
            ? { "$employee.department_en$": admin.department }
            : admin.role === "SubCityAdmin"
            ? { "$employee.section$": admin.section }
            : {},
      }),
      Feedback.count({ where }),
    ]);

    const ratings = await Rating.findAll({
      where:
        admin.role === "Admin"
          ? { "$employee.department_en$": admin.department }
          : admin.role === "SubCityAdmin"
          ? { "$employee.section$": admin.section }
          : {},
      include: [{ model: Employee, attributes: [] }],
    });
    const averageRating =
      ratings.length > 0
        ? (
            ratings.reduce(
              (sum, r) => sum + (r.courtesy + r.timeliness + r.knowledge) / 3,
              0
            ) / ratings.length
          ).toFixed(2)
        : 0;

    const feedback = await Feedback.findAll({ where });
    const sentimentCounts = feedback.reduce((acc, fb) => {
      const sentiment = getSentiment(fb.rating);
      acc[sentiment] = (acc[sentiment] || 0) + 1;
      return acc;
    }, {});

    res.json({
      totalComplaints: complaintCount,
      pendingComplaints,
      resolvedComplaints,
      totalEmployees: employeeCount,
      totalAdmins: adminCount,
      totalRatings: ratingCount,
      totalFeedback: feedbackCount,
      averageRating: parseFloat(averageRating),
      sentimentAnalysis: {
        positive: sentimentCounts.positive || 0,
        neutral: sentimentCounts.neutral || 0,
        negative: sentimentCounts.negative || 0,
      },
    });
  } catch (error) {
    console.error("Error fetching statistics:", error);
    res
      .status(500)
      .json({ message: "Error fetching statistics", error: error.message });
  }
};

// Get departments
const getDepartments = async (req, res) => {
  try {
    const { lang = "en" } = req.query;
    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const departments = await Employee.findAll({
      attributes: [
        [
          sequelize.fn("DISTINCT", sequelize.col(`department_${lang}`)),
          "department",
        ],
      ],
      where: { [`department_${lang}`]: { [Op.ne]: null } },
      raw: true,
    });

    res.json(departments.map((d) => d.department));
  } catch (error) {
    console.error("Error fetching departments:", error);
    res
      .status(500)
      .json({ message: "Error fetching departments", error: error.message });
  }
};

// Log admins (for debugging)
const logAdmins = async (req, res) => {
  try {
    const admins = await Admin.findAll({
      attributes: ["id", "username", "role", "section", "department"],
    });

    // Reset passwords for all admins to a known value (e.g., 'reset123') and log them
    const resetPassword = "reset123"; // Change this to a secure value
    const resetResults = await Promise.all(
      admins.map(async (admin) => {
        const hashedPassword = await bcrypt.hash(resetPassword, 10);
        await Admin.update(
          { password: hashedPassword },
          { where: { id: admin.id } }
        );
        console.log(
          `Reset password for ${admin.username} (ID: ${admin.id}, Role: ${admin.role}) to: ${resetPassword}`
        );
        return {
          id: admin.id,
          username: admin.username,
          role: admin.role,
          section: admin.section,
          department: admin.department,
          reset_password: resetPassword,
        };
      })
    );

    res.json({
      message: "Admin accounts listed and passwords reset",
      admins: resetResults,
    });
  } catch (error) {
    console.error("Error logging/resetting admin passwords:", error);
    res
      .status(500)
      .json({ message: "Error processing request", error: error.message });
  }
};

// Export report
const exportReport = async (req, res) => {
  try {
    const admin = req.user;
    const { lang = "en", date_from, date_to, type = "all" } = req.query;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const where = {};
    if (admin.role === "Admin") {
      where.department = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      where.section = admin.section;
    } // SuperAdmin: no filters
    if (date_from && date_to) {
      where.created_at = {
        [Op.between]: [new Date(date_from), new Date(date_to)],
      };
    }

    let reportData = {};
    if (type === "all" || type === "complaints") {
      const complaints = await Complaint.findAll({
        where,
        attributes: [
          "id",
          "phone_number",
          "section",
          "department",
          "employee_id",
          [`description_${lang}`, "description"],
          [`response_${lang}`, "response"],
          "tracking_code",
          "status",
          "created_at",
        ],
      });
      reportData.complaints = complaints;
    }

    if (type === "all" || type === "feedback") {
      const feedback = await Feedback.findAll({
        where,
        attributes: [
          "id",
          "phone_number",
          "section",
          "department",
          "employee_id",
          [`comment_${lang}`, "comment"],
          "rating",
          "created_at",
        ],
      });
      reportData.feedback = feedback.map((fb) => ({
        ...fb.get({ plain: true }),
        sentiment: getSentiment(fb.rating),
      }));
    }

    if (type === "all" || type === "ratings") {
      const ratings = await Rating.findAll({
        where:
          admin.role === "Admin"
            ? { "$employee.department_en$": admin.department }
            : admin.role === "SubCityAdmin"
            ? { "$employee.section$": admin.section }
            : {},
        include: [{ model: Employee, attributes: [] }],
        attributes: [
          "id",
          "phone_number",
          "courtesy",
          "timeliness",
          "knowledge",
          "overall_experience",
          "suggestions",
          "created_at",
          "employee_id",
        ],
      });
      reportData.ratings = ratings;
    }

    res.json(reportData);
  } catch (error) {
    console.error("Error exporting report:", error);
    res
      .status(500)
      .json({ message: "Error exporting report", error: error.message });
  }
};

// Get complaint trends for analytics
const getComplaintTrends = async (req, res) => {
  try {
    const admin = req.user;
    const { start_date, end_date, interval = "day" } = req.query;

    // Set default date range (last 30 days if not specified)
    const endDate = end_date ? new Date(end_date) : new Date();
    const startDate = start_date
      ? new Date(start_date)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    const where = {
      created_at: {
        [Op.between]: [startDate, endDate],
      },
    };

    // Apply role-based filters
    if (admin.role === "Admin") {
      where.department = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      where.section = admin.section;
    } // SuperAdmin: no additional filters

    // Get complaints within date range
    const complaints = await Complaint.findAll({
      where,
      attributes: ["created_at", "status", "department", "section"],
      order: [["created_at", "ASC"]],
    });

    // Group complaints by time interval
    const trendsData = [];
    const dateMap = new Map();

    complaints.forEach((complaint) => {
      const date = new Date(complaint.created_at);
      let dateKey;

      // Format date based on interval
      switch (interval) {
        case "week":
          // Get start of week (Monday)
          const startOfWeek = new Date(date);
          const day = startOfWeek.getDay();
          const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
          startOfWeek.setDate(diff);
          dateKey = startOfWeek.toISOString().split("T")[0];
          break;
        case "month":
          dateKey = `${date.getFullYear()}-${String(
            date.getMonth() + 1
          ).padStart(2, "0")}`;
          break;
        case "day":
        default:
          dateKey = date.toISOString().split("T")[0];
          break;
      }

      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, {
          date: dateKey,
          total: 0,
          pending: 0,
          resolved: 0,
        });
      }

      const entry = dateMap.get(dateKey);
      entry.total++;
      if (complaint.status === "pending") {
        entry.pending++;
      } else if (complaint.status === "resolved") {
        entry.resolved++;
      }
    });

    // Convert map to array and sort by date
    const trends = Array.from(dateMap.values()).sort(
      (a, b) => new Date(a.date) - new Date(b.date)
    );

    res.json(trends);
  } catch (error) {
    console.error("Error fetching complaint trends:", error);
    res.status(500).json({
      message: "Error fetching complaint trends",
      error: error.message,
    });
  }
};

// Get hierarchical location structure
const getLocationHierarchy = async (req, res) => {
  try {
    const admin = req.user;

    let locations = {};

    if (admin.role === "SuperAdmin") {
      // SuperAdmin can see all cities and subcities
      locations = {
        "Addis Ababa": {
          subcities: [
            "Arada",
            "Kirkos",
            "Lideta",
            "Bole",
            "Yeka",
            "Addis Ketema",
          ],
          departments: [
            "Control and Awareness Department",
            "Engineering Department",
            "Support Administration Department",
            "Control Center Department",
          ],
        },
      };
    } else if (admin.role === "SubCityAdmin") {
      // SubCityAdmin can only see their specific subcity
      locations = {
        [admin.city]: {
          subcities: [admin.subcity],
          departments: [
            "Control and Awareness Department",
            "Engineering Department",
            "Support Administration Department",
            "Control Center Department",
          ],
        },
      };
    } else if (admin.role === "Admin") {
      // Department admin can see their department across all locations
      locations = {
        "Addis Ababa": {
          subcities: [
            "Arada",
            "Kirkos",
            "Lideta",
            "Bole",
            "Yeka",
            "Addis Ketema",
          ],
          departments: [admin.department],
        },
      };
    }

    res.json({
      success: true,
      data: {
        locations,
        admin_scope: {
          role: admin.role,
          city: admin.city,
          subcity: admin.subcity,
          department: admin.department,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching location hierarchy:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch location hierarchy",
    });
  }
};

// Get statistics with city/subcity filtering
const getStatisticsWithLocation = async (req, res) => {
  try {
    const { city, subcity } = req.query;
    const admin = req.user;

    let whereClause = {};

    // Apply role-based filtering
    if (admin.role === "SubCityAdmin") {
      whereClause.city = admin.city;
      whereClause.subcity = admin.subcity;
    } else if (admin.role === "Admin") {
      whereClause.department = admin.department;
    } else if (admin.role === "SuperAdmin") {
      if (city) whereClause.city = city;
      if (subcity) whereClause.subcity = subcity;
    }

    // Get counts from different tables
    const [employeeCount, complaintCount, feedbackCount, ratingCount] =
      await Promise.all([
        Employee.count({ where: whereClause }),
        // Add similar counts for other models when they have city/subcity fields
        0, // Placeholder for complaint count
        0, // Placeholder for feedback count
        0, // Placeholder for rating count
      ]);

    res.json({
      success: true,
      data: {
        employees: employeeCount,
        complaints: complaintCount,
        feedback: feedbackCount,
        ratings: ratingCount,
        filters_applied: whereClause,
      },
    });
  } catch (error) {
    console.error("Error fetching statistics:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch statistics",
    });
  }
};

// Get ratings (Admin view)
const getRatingsAdmin = async (req, res) => {
  try {
    const { lang = "en", date_from, date_to } = req.query;
    const admin = req.user;

    let whereClause = {};

    // Apply role-based filtering
    if (admin.role === "SubCityAdmin") {
      // For ratings, we need to filter by employee's subcity
      whereClause = {};
    } else if (admin.role === "Admin") {
      // For admin role, filter by department through employee relationship
      whereClause = {};
    }

    // Apply date filtering
    if (date_from || date_to) {
      whereClause.created_at = {};
      if (date_from) whereClause.created_at[Op.gte] = new Date(date_from);
      if (date_to) whereClause.created_at[Op.lte] = new Date(date_to);
    }

    const ratings = await Rating.findAll({
      where: whereClause,
      include: [
        {
          model: Employee,
          required: true,
          attributes: [
            `first_name_${lang}`,
            `middle_name_${lang}`,
            `last_name_${lang}`,
            `department_${lang}`,
            "section",
            "city",
            "subcity",
          ],
          where:
            admin.role === "SubCityAdmin"
              ? { subcity: admin.subcity }
              : admin.role === "Admin"
              ? { [`department_${lang}`]: admin.department }
              : {},
        },
      ],
      order: [["created_at", "DESC"]],
    });

    // Transform data to match API spec
    const transformedRatings = ratings.map((rating) => ({
      id: rating.id,
      employee_name: `${rating.Employee?.[`first_name_${lang}`] || ""} ${
        rating.Employee?.[`last_name_${lang}`] || ""
      }`.trim(),
      employee_id: rating.employee_id,
      department:
        rating.Employee?.[`department_${lang}`] || rating.Employee?.section,
      phone_number: rating.phone_number,
      courtesy: rating.courtesy,
      timeliness: rating.timeliness,
      knowledge: rating.knowledge,
      overall_experience: rating.overall_experience,
      suggestions: rating.suggestions,
      section: rating.Employee?.section,
      city: rating.Employee?.city,
      subcity: rating.Employee?.subcity,
      created_at: rating.created_at,
    }));

    res.json(transformedRatings);
  } catch (error) {
    console.error("Error fetching ratings:", error);
    res
      .status(500)
      .json({ message: "Error fetching ratings", error: error.message });
  }
};

// Get public ratings (Admin view)
const getPublicRatingsAdmin = async (req, res) => {
  try {
    const admin = req.user;

    let whereClause = {};

    // Apply role-based filtering
    if (admin.role === "SubCityAdmin") {
      // For subcity admin, we'd need location info in public ratings
      // For now, filter by department if available
    } else if (admin.role === "Admin") {
      whereClause.department = admin.department;
    }

    const ratings = await PublicRating.findAll({
      where: whereClause,
      include: [
        {
          model: Employee,
          required: false,
          as: "employee",
        },
      ],
      order: [["created_at", "DESC"]],
    });

    res.json(ratings);
  } catch (error) {
    console.error("Error fetching public ratings:", error);
    res
      .status(500)
      .json({ message: "Error fetching public ratings", error: error.message });
  }
};

// Respond to feedback
const respondToFeedback = async (req, res) => {
  try {
    const { id } = req.params;
    const { response, lang = "en" } = req.body;
    const admin = req.user;

    if (!response || response.length < 10) {
      return res
        .status(400)
        .json({ message: "Response must be at least 10 characters long" });
    }

    const feedback = await Feedback.findByPk(id);
    if (!feedback) {
      return res.status(404).json({ message: "Feedback not found" });
    }

    // Check role-based access
    const employee = await Employee.findByPk(feedback.employee_id);
    if (admin.role === "SubCityAdmin" && employee?.subcity !== admin.subcity) {
      return res
        .status(403)
        .json({ message: "Cannot respond to feedback from another subcity" });
    }
    if (admin.role === "Admin" && employee?.department !== admin.department) {
      return res.status(403).json({
        message: "Cannot respond to feedback from another department",
      });
    }

    // Update feedback with response
    const responseField = `response_${lang}`;
    await feedback.update({
      [responseField]: response,
      status: "responded",
      responded_at: new Date(),
    });

    res.json({
      message: "Response added successfully",
      feedback: {
        id: feedback.id,
        status: "responded",
        response: response,
        responded_at: feedback.responded_at,
      },
    });
  } catch (error) {
    console.error("Error responding to feedback:", error);
    res
      .status(500)
      .json({ message: "Error responding to feedback", error: error.message });
  }
};

// Respond to public feedback
const respondToPublicFeedback = async (req, res) => {
  try {
    const { id } = req.params;
    const { response } = req.body;
    const admin = req.user;

    if (!response || response.length < 10) {
      return res
        .status(400)
        .json({ message: "Response must be at least 10 characters long" });
    }

    const feedback = await PublicFeedback.findByPk(id);
    if (!feedback) {
      return res.status(404).json({ message: "Public feedback not found" });
    }

    // Check role-based access
    if (admin.role === "Admin" && feedback.department !== admin.department) {
      return res.status(403).json({
        message: "Cannot respond to feedback from another department",
      });
    }

    await feedback.update({
      response_text: response,
      responded_by: admin.id,
      responded_at: new Date(),
      status: "responded",
    });

    res.json({
      message: "Response added successfully",
      feedback: {
        id: feedback.id,
        status: "responded",
        response_text: response,
        responded_at: feedback.responded_at,
      },
    });
  } catch (error) {
    console.error("Error responding to public feedback:", error);
    res.status(500).json({
      message: "Error responding to public feedback",
      error: error.message,
    });
  }
};

// Export employees specifically
const exportEmployees = async (req, res) => {
  try {
    const admin = req.user;
    const { lang = "en", format = "csv" } = req.query;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const where = {};
    if (admin.role === "Admin") {
      where.department = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      where.section = admin.section;
    } // SuperAdmin: no filters

    const employees = await Employee.findAll({
      where,
      attributes: [
        "id",
        "first_name",
        "middle_name",
        "last_name",
        "office_number",
        "floor_number",
        "position",
        "department",
        "section",
        "city",
        "subcity",
        "created_at",
      ],
      order: [["created_at", "DESC"]],
    });

    // Set appropriate headers for file download
    const filename = `employees-${Date.now()}.${
      format === "excel" ? "xlsx" : format
    }`;

    if (format === "csv") {
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      // CSV headers
      const csvHeaders =
        "ID,First Name,Middle Name,Last Name,Office Number,Floor Number,Position,Department,Section,City,Subcity,Created At\n";
      let csvContent = csvHeaders;

      employees.forEach((emp) => {
        csvContent += `${emp.id},"${emp.first_name}","${
          emp.middle_name || ""
        }","${emp.last_name}","${emp.office_number}",${emp.floor_number},"${
          emp.position
        }","${emp.department}","${emp.section}","${emp.city || ""}","${
          emp.subcity || ""
        }","${emp.created_at}"\n`;
      });

      res.send(csvContent);
    } else {
      // For other formats, return JSON for now
      res.json({ employees });
    }
  } catch (error) {
    console.error("Error exporting employees:", error);
    res
      .status(500)
      .json({ message: "Error exporting employees", error: error.message });
  }
};

// Export complaints specifically
const exportComplaints = async (req, res) => {
  try {
    const admin = req.user;
    const { lang = "en", format = "csv" } = req.query;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const where = {};
    if (admin.role === "Admin") {
      where.department = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      where.section = admin.section;
    } // SuperAdmin: no filters

    const complaints = await Complaint.findAll({
      where,
      attributes: [
        "id",
        "phone_number",
        "section",
        "department",
        "employee_id",
        [`description_${lang}`, "description"],
        [`response_${lang}`, "response"],
        "tracking_code",
        "status",
        "created_at",
      ],
      order: [["created_at", "DESC"]],
    });

    // Set appropriate headers for file download
    const filename = `complaints-${Date.now()}.${
      format === "excel" ? "xlsx" : format
    }`;

    if (format === "csv") {
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      // CSV headers
      const csvHeaders =
        "ID,Phone Number,Section,Department,Employee ID,Description,Response,Tracking Code,Status,Created At\n";
      let csvContent = csvHeaders;

      complaints.forEach((complaint) => {
        csvContent += `${complaint.id},"${complaint.phone_number}","${
          complaint.section
        }","${complaint.department}",${complaint.employee_id},"${(
          complaint.description || ""
        ).replace(/"/g, '""')}","${(complaint.response || "").replace(
          /"/g,
          '""'
        )}","${complaint.tracking_code}","${complaint.status}","${
          complaint.created_at
        }"\n`;
      });

      res.send(csvContent);
    } else {
      // For other formats, return JSON for now
      res.json({ complaints });
    }
  } catch (error) {
    console.error("Error exporting complaints:", error);
    res
      .status(500)
      .json({ message: "Error exporting complaints", error: error.message });
  }
};

// Export feedback specifically
const exportFeedback = async (req, res) => {
  try {
    const admin = req.user;
    const { lang = "en", format = "csv" } = req.query;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const where = {};
    if (admin.role === "Admin") {
      where.department = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      where.section = admin.section;
    } // SuperAdmin: no filters

    const feedback = await Feedback.findAll({
      where,
      attributes: [
        "id",
        "phone_number",
        "section",
        "department",
        "employee_id",
        [`comment_${lang}`, "comment"],
        "rating",
        "created_at",
      ],
      order: [["created_at", "DESC"]],
    });

    // Set appropriate headers for file download
    const filename = `feedback-${Date.now()}.${
      format === "excel" ? "xlsx" : format
    }`;

    if (format === "csv") {
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      // CSV headers
      const csvHeaders =
        "ID,Phone Number,Section,Department,Employee ID,Comment,Rating,Created At\n";
      let csvContent = csvHeaders;

      feedback.forEach((fb) => {
        csvContent += `${fb.id},"${fb.phone_number}","${fb.section}","${
          fb.department
        }",${fb.employee_id},"${(fb.comment || "").replace(/"/g, '""')}",${
          fb.rating
        },"${fb.created_at}"\n`;
      });

      res.send(csvContent);
    } else {
      // For other formats, return JSON for now
      res.json({ feedback });
    }
  } catch (error) {
    console.error("Error exporting feedback:", error);
    res
      .status(500)
      .json({ message: "Error exporting feedback", error: error.message });
  }
};

module.exports = {
  adminLogin,
  createAdmin,
  updateAdminProfile,
  requestPasswordReset,
  resetPassword,
  getStatistics,
  getDepartments,
  logAdmins,
  exportReport,
  getComplaintTrends,
  getLocationHierarchy,
  getStatisticsWithLocation,
  getRatingsAdmin,
  getPublicRatingsAdmin,
  respondToFeedback,
  respondToPublicFeedback,
  exportEmployees,
  exportComplaints,
  exportFeedback,
};
