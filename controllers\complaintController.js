const { Complaint, Employee } = require("../models");
const { validateLanguage, addVoiceFileUrl } = require("../utils/helpers");
const { v4: uuidv4 } = require("uuid");
const { Op } = require("sequelize");

// Create complaint
const createComplaint = async (req, res) => {
  try {
    const {
      complainant_name,
      phone_number,
      section,
      kebele,
      province,
      department,
      employee_id,
      description,
      desired_action,
      created_at,
      lang = "en",
    } = req.body;
    const voice_file = req.file ? req.file.filename : null;
    const tracking_code = uuidv4();

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }
    if (!phone_number || !section) {
      return res.status(400).json({
        message: "Phone number and section are required",
      });
    }

    const complaintData = {
      complainant_name,
      phone_number,
      section,
      kebele,
      province,
      department,
      employee_id: employee_id ? parseInt(employee_id) : null,
      voice_file,
      tracking_code,
      status: "pending",
      [`description_${lang}`]: description,
      [`desired_action_${lang}`]: desired_action,
      created_at: created_at ? new Date(created_at) : new Date(),
    };

    await Complaint.create(complaintData);

    res.status(201).json({
      tracking_code,
      message: "Complaint submitted successfully",
      voice_url: voice_file ? `/uploads/voice_complaints/${voice_file}` : null,
    });
  } catch (error) {
    console.error("Error submitting complaint:", error);
    res
      .status(500)
      .json({ message: "Error submitting complaint", error: error.message });
  }
};

// Get complaints (public)
const getComplaints = async (req, res) => {
  try {
    const { phone_number, tracking_code, lang = "en" } = req.query;

    if (!phone_number && !tracking_code) {
      return res
        .status(400)
        .json({ message: "Phone number or tracking code required" });
    }

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const where = phone_number ? { phone_number } : { tracking_code };
    const complaints = await Complaint.findAll({
      where,
      attributes: [
        "id",
        "complainant_name",
        "phone_number",
        "section",
        "kebele",
        "province",
        "department",
        "employee_id",
        [`description_${lang}`, "description"],
        [`desired_action_${lang}`, "desired_action"],
        [`response_${lang}`, "response"],
        "voice_file",
        "tracking_code",
        "status",
        "created_at",
      ],
      include: [
        {
          model: Employee,
          attributes: [
            "id",
            [`first_name_${lang}`, "first_name"],
            [`middle_name_${lang}`, "middle_name"],
            [`last_name_${lang}`, "last_name"],
            "office_number",
            "floor_number",
            [`position_${lang}`, "position"],
            [`department_${lang}`, "department"],
            "section",
          ],
        },
      ],
    });

    const complaintsWithUrls = complaints.map(addVoiceFileUrl);
    res.json(complaintsWithUrls);
  } catch (error) {
    console.error("Error fetching complaints:", error);
    res
      .status(500)
      .json({ message: "Error fetching complaints", error: error.message });
  }
};

// Get complaints for admin
const getComplaintsAdmin = async (req, res) => {
  try {
    const { lang = "en" } = req.query;
    const admin = req.user;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }

    const where = {};
    if (admin.role === "Admin") {
      where.department = admin.department;
    } else if (admin.role === "SubCityAdmin") {
      where.section = admin.subcity; // Use subcity for SubCityAdmin filtering
    } // SuperAdmin: no filters

    const complaints = await Complaint.findAll({
      where,
      attributes: [
        "id",
        "complainant_name",
        "phone_number",
        "section",
        "kebele",
        "province",
        "department",
        "employee_id",
        [`description_${lang}`, "description"],
        [`desired_action_${lang}`, "desired_action"],
        [`response_${lang}`, "response"],
        "voice_file",
        "tracking_code",
        "status",
        "created_at",
      ],
      include: [
        {
          model: Employee,
          attributes: [
            "id",
            [`first_name_${lang}`, "first_name"],
            [`middle_name_${lang}`, "middle_name"],
            [`last_name_${lang}`, "last_name"],
            "office_number",
            "floor_number",
            [`position_${lang}`, "position"],
            [`department_${lang}`, "department"],
            "section",
          ],
        },
      ],
      order: [["created_at", "DESC"]],
    });

    const complaintsWithUrls = complaints.map(addVoiceFileUrl);
    res.json(complaintsWithUrls);
  } catch (error) {
    console.error("Error fetching complaints:", error);
    res
      .status(500)
      .json({ message: "Error fetching complaints", error: error.message });
  }
};

// Resolve complaint
const resolveComplaint = async (req, res) => {
  try {
    const { id } = req.params;
    const { response, lang = "en" } = req.body;
    const admin = req.user;

    if (!validateLanguage(lang)) {
      return res
        .status(400)
        .json({ message: "Invalid language. Use en, am, or af." });
    }
    if (!response) {
      return res
        .status(400)
        .json({ message: "Response description is required" });
    }

    const complaint = await Complaint.findByPk(id);
    if (!complaint) {
      return res.status(404).json({ message: "Complaint not found" });
    }

    if (admin.role === "Admin" && complaint.department !== admin.department) {
      return res
        .status(403)
        .json({ message: "Cannot resolve complaint from another department" });
    }
    if (admin.role === "SubCityAdmin" && complaint.section !== admin.section) {
      return res
        .status(403)
        .json({ message: "Cannot resolve complaint from another section" });
    }

    await Complaint.update(
      { status: "resolved", [`response_${lang}`]: response },
      { where: { id } }
    );

    res.json({ message: "Complaint resolved successfully" });
  } catch (error) {
    console.error("Error resolving complaint:", error);
    res
      .status(500)
      .json({ message: "Error resolving complaint", error: error.message });
  }
};

module.exports = {
  createComplaint,
  getComplaints,
  getComplaintsAdmin,
  resolveComplaint,
};
