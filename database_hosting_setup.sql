-- =====================================
-- OFFICE MANAGEMENT SYSTEM - HOSTING DATABASE SETUP
-- =====================================
-- Complete SQL script for hosting server deployment
-- Copy and paste this entire script into your hosting database manager
-- 
-- Instructions:
-- 1. Create a new database on your hosting server
-- 2. Copy this entire script
-- 3. Paste and execute in your database manager (phpMyAdmin, etc.)
-- 4. Update your .env file with the database credentials
-- =====================================

-- Set character set and collation
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- =====================================
-- CORE SYSTEM TABLES
-- =====================================

-- Departments table - Multi-language support
CREATE TABLE IF NOT EXISTS departments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name_en VARCHAR(100) NOT NULL COMMENT 'Department name in English',
  name_am VARCHAR(100) NOT NULL COMMENT 'Department name in Amharic',
  name_af VARCHAR(100) NOT NULL COMMENT 'Department name in Afan Oromo',
  code VARCHAR(20) NOT NULL UNIQUE COMMENT 'Unique department code',
  description_en TEXT COMMENT 'Department description in English',
  description_am TEXT COMMENT 'Department description in Amharic',
  description_af TEXT COMMENT 'Department description in Afan Oromo',
  is_active BOOLEAN DEFAULT TRUE COMMENT 'Department active status',
  contact_email VARCHAR(100) COMMENT 'Department contact email',
  contact_phone VARCHAR(15) COMMENT 'Department contact phone',
  head_name VARCHAR(100) COMMENT 'Department head name',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_dept_code (code),
  INDEX idx_dept_active (is_active),
  INDEX idx_dept_name_en (name_en)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Departments table with multi-language support';

-- Offices table - Office locations within departments
CREATE TABLE IF NOT EXISTS offices (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT 'Office name',
  name_amharic VARCHAR(100) COMMENT 'Office name in Amharic',
  name_afan_oromo VARCHAR(100) COMMENT 'Office name in Afan Oromo',
  office_number VARCHAR(20) COMMENT 'Office number/identifier',
  department_id INT NOT NULL COMMENT 'Reference to department',
  description TEXT COMMENT 'Office description',
  floor VARCHAR(10) COMMENT 'Floor number/identifier',
  location VARCHAR(200) COMMENT 'Physical location description',
  is_active BOOLEAN DEFAULT TRUE COMMENT 'Office active status',
  phone VARCHAR(15) COMMENT 'Office direct phone',
  email VARCHAR(100) COMMENT 'Office email',
  services_offered TEXT COMMENT 'List of services offered',
  opening_hours VARCHAR(100) DEFAULT '8:00 AM - 5:00 PM' COMMENT 'Office hours',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE RESTRICT ON UPDATE CASCADE,
  INDEX idx_office_dept (department_id),
  INDEX idx_office_active (is_active),
  INDEX idx_office_number (office_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Offices table with department relationships';

-- =====================================
-- ADMIN SYSTEM TABLES
-- =====================================

-- Admins table - Role-based admin access
CREATE TABLE IF NOT EXISTS admins (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(255) NOT NULL UNIQUE COMMENT 'Admin username',
  password VARCHAR(255) NOT NULL COMMENT 'Hashed password',
  email VARCHAR(100) UNIQUE COMMENT 'Admin email address',
  role ENUM('SuperAdmin', 'SubCityAdmin', 'Admin') NOT NULL COMMENT 'Admin role level',
  first_name VARCHAR(50) COMMENT 'Admin first name',
  last_name VARCHAR(50) COMMENT 'Admin last name',
  city VARCHAR(100) COMMENT 'City jurisdiction',
  subcity VARCHAR(100) COMMENT 'Subcity jurisdiction',
  section VARCHAR(255) COMMENT 'Section/area responsibility',
  department VARCHAR(255) COMMENT 'Department assignment',
  phone VARCHAR(15) COMMENT 'Contact phone',
  profile_picture VARCHAR(255) COMMENT 'Profile picture URL',
  is_active BOOLEAN DEFAULT TRUE COMMENT 'Admin account status',
  last_login TIMESTAMP NULL COMMENT 'Last login timestamp',
  failed_login_attempts INT DEFAULT 0 COMMENT 'Failed login counter',
  account_locked_until TIMESTAMP NULL COMMENT 'Account lock expiry',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_admin_username (username),
  INDEX idx_admin_role (role),
  INDEX idx_admin_city_subcity (city, subcity),
  INDEX idx_admin_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Admin users with role-based access control';

-- Password reset table
CREATE TABLE IF NOT EXISTS password_resets (
  id INT AUTO_INCREMENT PRIMARY KEY,
  admin_id INT NOT NULL COMMENT 'Reference to admin',
  token VARCHAR(255) NOT NULL UNIQUE COMMENT 'Reset token',
  expires_at TIMESTAMP NOT NULL COMMENT 'Token expiry time',
  used BOOLEAN DEFAULT FALSE COMMENT 'Token usage status',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
  INDEX idx_reset_token (token),
  INDEX idx_reset_admin (admin_id),
  INDEX idx_reset_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Password reset tokens for admin users';

-- =====================================
-- EMPLOYEE SYSTEM TABLES
-- =====================================

-- Employees table - Multi-language employee information
CREATE TABLE IF NOT EXISTS employees (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_id VARCHAR(20) UNIQUE COMMENT 'Unique employee identifier',
  first_name_en VARCHAR(50) COMMENT 'First name in English',
  first_name_am VARCHAR(50) COMMENT 'First name in Amharic',
  first_name_af VARCHAR(50) COMMENT 'First name in Afan Oromo',
  middle_name_en VARCHAR(50) COMMENT 'Middle name in English',
  middle_name_am VARCHAR(50) COMMENT 'Middle name in Amharic',
  middle_name_af VARCHAR(50) COMMENT 'Middle name in Afan Oromo',
  last_name_en VARCHAR(50) COMMENT 'Last name in English',
  last_name_am VARCHAR(50) COMMENT 'Last name in Amharic',
  last_name_af VARCHAR(50) COMMENT 'Last name in Afan Oromo',
  office_id INT COMMENT 'Reference to office',
  office_number VARCHAR(10) COMMENT 'Office room number',
  floor_number INT COMMENT 'Floor number',
  position_en VARCHAR(100) COMMENT 'Position title in English',
  position_am VARCHAR(100) COMMENT 'Position title in Amharic',
  position_af VARCHAR(100) COMMENT 'Position title in Afan Oromo',
  department_en VARCHAR(100) COMMENT 'Department name in English',
  department_am VARCHAR(100) COMMENT 'Department name in Amharic',
  department_af VARCHAR(100) COMMENT 'Department name in Afan Oromo',
  section VARCHAR(255) NOT NULL COMMENT 'Section/Subcity assignment',
  city VARCHAR(100) COMMENT 'City location',
  subcity VARCHAR(100) COMMENT 'Subcity location',
  email VARCHAR(100) COMMENT 'Employee email',
  phone VARCHAR(15) COMMENT 'Employee phone',
  profile_picture VARCHAR(255) COMMENT 'Profile picture URL',
  bio_en TEXT COMMENT 'Employee bio in English',
  bio_am TEXT COMMENT 'Employee bio in Amharic',
  bio_af TEXT COMMENT 'Employee bio in Afan Oromo',
  specializations TEXT COMMENT 'Employee specializations/skills',
  years_of_service INT DEFAULT 0 COMMENT 'Years of service',
  education_level VARCHAR(100) COMMENT 'Education level',
  is_active BOOLEAN DEFAULT TRUE COMMENT 'Employee active status',
  hire_date DATE COMMENT 'Date of hire',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (office_id) REFERENCES offices(id) ON DELETE SET NULL ON UPDATE CASCADE,
  INDEX idx_emp_id (employee_id),
  INDEX idx_emp_section (section),
  INDEX idx_emp_city_subcity (city, subcity),
  INDEX idx_emp_dept (department_en),
  INDEX idx_emp_active (is_active),
  INDEX idx_emp_name_en (first_name_en, last_name_en)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Employee directory with multi-language support';

-- =====================================
-- COMPLAINT SYSTEM TABLES
-- =====================================

-- Internal complaints table (Admin portal)
CREATE TABLE IF NOT EXISTS complaints (
  id INT AUTO_INCREMENT PRIMARY KEY,
  complaint_id VARCHAR(20) UNIQUE NOT NULL COMMENT 'Unique complaint identifier',
  title_en VARCHAR(200) COMMENT 'Complaint title in English',
  title_am VARCHAR(200) COMMENT 'Complaint title in Amharic',
  title_af VARCHAR(200) COMMENT 'Complaint title in Afan Oromo',
  description_en TEXT COMMENT 'Complaint description in English',
  description_am TEXT COMMENT 'Complaint description in Amharic',
  description_af TEXT COMMENT 'Complaint description in Afan Oromo',
  employee_id INT COMMENT 'Target employee',
  department_id INT COMMENT 'Target department',
  complainant_name VARCHAR(100) COMMENT 'Complainant name',
  complainant_email VARCHAR(100) COMMENT 'Complainant email',
  complainant_phone VARCHAR(15) COMMENT 'Complainant phone',
  status ENUM('pending', 'in_progress', 'resolved', 'closed') DEFAULT 'pending',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
  category VARCHAR(100) COMMENT 'Complaint category',
  assigned_to INT COMMENT 'Assigned admin ID',
  resolution_notes TEXT COMMENT 'Resolution details',
  attachments JSON COMMENT 'File attachments',
  resolved_at TIMESTAMP NULL COMMENT 'Resolution timestamp',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
  FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
  FOREIGN KEY (assigned_to) REFERENCES admins(id) ON DELETE SET NULL,
  INDEX idx_complaint_id (complaint_id),
  INDEX idx_complaint_status (status),
  INDEX idx_complaint_employee (employee_id),
  INDEX idx_complaint_dept (department_id),
  INDEX idx_complaint_date (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Internal complaint management system';

-- Public complaints table (Citizen portal)
CREATE TABLE IF NOT EXISTS public_complaints (
  id INT AUTO_INCREMENT PRIMARY KEY,
  reference_number VARCHAR(20) UNIQUE NOT NULL COMMENT 'Public reference number',
  full_name VARCHAR(100) NOT NULL COMMENT 'Complainant full name',
  phone_number VARCHAR(15) NOT NULL COMMENT 'Complainant phone',
  email VARCHAR(100) COMMENT 'Complainant email',
  complaint_description TEXT NOT NULL COMMENT 'Complaint details',
  department VARCHAR(100) COMMENT 'Target department',
  subcity VARCHAR(100) COMMENT 'Subcity location',
  service_type VARCHAR(100) COMMENT 'Service related to complaint',
  complaint_type ENUM('service_quality', 'staff_behavior', 'facility_issue', 'process_delay', 'other') DEFAULT 'other',
  voice_note VARCHAR(255) COMMENT 'Voice complaint file path',
  status ENUM('submitted', 'under_review', 'investigating', 'resolved', 'closed') DEFAULT 'submitted',
  priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
  assigned_admin INT COMMENT 'Assigned admin for handling',
  admin_notes TEXT COMMENT 'Internal admin notes',
  resolution_summary TEXT COMMENT 'Resolution summary for citizen',
  citizen_satisfaction_rating INT COMMENT 'Citizen satisfaction (1-5)',
  follow_up_required BOOLEAN DEFAULT FALSE,
  follow_up_date DATE COMMENT 'Scheduled follow-up date',
  resolved_at TIMESTAMP NULL COMMENT 'Resolution timestamp',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (assigned_admin) REFERENCES admins(id) ON DELETE SET NULL,
  INDEX idx_pub_complaint_ref (reference_number),
  INDEX idx_pub_complaint_phone (phone_number),
  INDEX idx_pub_complaint_status (status),
  INDEX idx_pub_complaint_dept (department),
  INDEX idx_pub_complaint_subcity (subcity),
  INDEX idx_pub_complaint_type (complaint_type),
  INDEX idx_pub_complaint_date (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Public citizen complaint system';

-- =====================================
-- FEEDBACK SYSTEM TABLES
-- =====================================

-- Internal feedback table (Admin portal)
CREATE TABLE IF NOT EXISTS feedbacks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_id INT COMMENT 'Target employee',
  department_id INT COMMENT 'Target department',
  feedback_type ENUM('suggestion', 'compliment', 'concern', 'idea') DEFAULT 'suggestion',
  comment_en TEXT COMMENT 'Feedback in English',
  comment_am TEXT COMMENT 'Feedback in Amharic',
  comment_af TEXT COMMENT 'Feedback in Afan Oromo',
  rating INT CHECK (rating >= 1 AND rating <= 5) COMMENT 'Rating 1-5',
  submitter_name VARCHAR(100) COMMENT 'Feedback submitter',
  submitter_email VARCHAR(100) COMMENT 'Submitter email',
  is_anonymous BOOLEAN DEFAULT FALSE COMMENT 'Anonymous feedback flag',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
  FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
  INDEX idx_feedback_employee (employee_id),
  INDEX idx_feedback_dept (department_id),
  INDEX idx_feedback_type (feedback_type),
  INDEX idx_feedback_rating (rating),
  INDEX idx_feedback_date (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Internal feedback system';

-- Public feedback table (Citizen portal)
CREATE TABLE IF NOT EXISTS public_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY,
  full_name VARCHAR(100) NOT NULL COMMENT 'Feedback provider name',
  phone_number VARCHAR(15) NOT NULL COMMENT 'Contact phone',
  email VARCHAR(100) COMMENT 'Contact email',
  subject VARCHAR(200) NOT NULL COMMENT 'Feedback subject',
  feedback_text TEXT NOT NULL COMMENT 'Detailed feedback',
  department VARCHAR(100) COMMENT 'Target department',
  subcity VARCHAR(100) COMMENT 'Subcity location',
  service_experienced VARCHAR(100) COMMENT 'Service received',
  feedback_type ENUM('suggestion', 'compliment', 'concern', 'service_improvement', 'general') DEFAULT 'general',
  overall_satisfaction INT CHECK (overall_satisfaction >= 1 AND overall_satisfaction <= 5) COMMENT 'Overall satisfaction rating',
  would_recommend BOOLEAN COMMENT 'Would recommend service',
  is_anonymous BOOLEAN DEFAULT FALSE COMMENT 'Anonymous feedback flag',
  admin_response TEXT COMMENT 'Admin response to feedback',
  response_date TIMESTAMP NULL COMMENT 'Response timestamp',
  status ENUM('new', 'reviewed', 'responded', 'archived') DEFAULT 'new',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_pub_feedback_phone (phone_number),
  INDEX idx_pub_feedback_dept (department),
  INDEX idx_pub_feedback_subcity (subcity),
  INDEX idx_pub_feedback_type (feedback_type),
  INDEX idx_pub_feedback_satisfaction (overall_satisfaction),
  INDEX idx_pub_feedback_status (status),
  INDEX idx_pub_feedback_date (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Public citizen feedback system';

-- =====================================
-- RATING SYSTEM TABLES
-- =====================================

-- Internal ratings table (Admin portal)
CREATE TABLE IF NOT EXISTS ratings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_id INT COMMENT 'Rated employee',
  department_id INT COMMENT 'Rated department',
  rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5) COMMENT 'Rating value 1-5',
  comment TEXT COMMENT 'Rating comment',
  rater_name VARCHAR(100) COMMENT 'Person giving rating',
  rater_email VARCHAR(100) COMMENT 'Rater email',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
  FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
  INDEX idx_rating_employee (employee_id),
  INDEX idx_rating_dept (department_id),
  INDEX idx_rating_value (rating),
  INDEX idx_rating_date (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Internal rating system';

-- Public ratings table (Citizen portal)
CREATE TABLE IF NOT EXISTS public_ratings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  full_name VARCHAR(100) NOT NULL COMMENT 'Rater full name',
  phone_number VARCHAR(15) NOT NULL COMMENT 'Contact phone',
  email VARCHAR(100) COMMENT 'Contact email',
  service_type VARCHAR(100) NOT NULL COMMENT 'Service being rated',
  department VARCHAR(100) COMMENT 'Department being rated',
  subcity VARCHAR(100) COMMENT 'Subcity location',
  overall_rating INT NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5) COMMENT 'Overall service rating',
  staff_professionalism INT CHECK (staff_professionalism >= 1 AND staff_professionalism <= 5) COMMENT 'Staff rating',
  service_speed INT CHECK (service_speed >= 1 AND service_speed <= 5) COMMENT 'Speed rating',
  facility_quality INT CHECK (facility_quality >= 1 AND facility_quality <= 5) COMMENT 'Facility rating',
  communication_quality INT CHECK (communication_quality >= 1 AND communication_quality <= 5) COMMENT 'Communication rating',
  additional_comments TEXT COMMENT 'Additional feedback',
  visit_date DATE COMMENT 'Service visit date',
  wait_time_minutes INT COMMENT 'Waiting time in minutes',
  issue_resolved BOOLEAN COMMENT 'Was issue resolved',
  would_recommend BOOLEAN COMMENT 'Would recommend service',
  is_verified BOOLEAN DEFAULT FALSE COMMENT 'Rating verification status',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_pub_rating_overall (overall_rating),
  INDEX idx_pub_rating_dept (department),
  INDEX idx_pub_rating_subcity (subcity),
  INDEX idx_pub_rating_service (service_type),
  INDEX idx_pub_rating_phone (phone_number),
  INDEX idx_pub_rating_date (visit_date),
  INDEX idx_pub_rating_verified (is_verified)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Public service rating system';

-- =====================================
-- SYSTEM MONITORING TABLES
-- =====================================

-- Activity logs table
CREATE TABLE IF NOT EXISTS activity_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  admin_id INT COMMENT 'Admin who performed action',
  action VARCHAR(100) NOT NULL COMMENT 'Action performed',
  entity_type VARCHAR(50) NOT NULL COMMENT 'Type of entity affected',
  entity_id INT COMMENT 'ID of affected entity',
  details JSON COMMENT 'Additional action details',
  ip_address VARCHAR(45) COMMENT 'IP address of request',
  user_agent TEXT COMMENT 'Browser/client information',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL,
  INDEX idx_log_admin (admin_id),
  INDEX idx_log_action (action),
  INDEX idx_log_entity (entity_type, entity_id),
  INDEX idx_log_date (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='System activity logging';

-- System settings table
CREATE TABLE IF NOT EXISTS system_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT 'Setting identifier',
  setting_value TEXT COMMENT 'Setting value',
  setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  description TEXT COMMENT 'Setting description',
  category VARCHAR(50) COMMENT 'Setting category',
  is_public BOOLEAN DEFAULT FALSE COMMENT 'Publicly accessible setting',
  updated_by INT COMMENT 'Admin who last updated',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (updated_by) REFERENCES admins(id) ON DELETE SET NULL,
  INDEX idx_setting_key (setting_key),
  INDEX idx_setting_category (category),
  INDEX idx_setting_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='System configuration settings';

-- =====================================
-- PERFORMANCE INDEXES
-- =====================================

-- Additional performance indexes
CREATE INDEX IF NOT EXISTS idx_complaints_status_date ON complaints(status, created_at);
CREATE INDEX IF NOT EXISTS idx_public_complaints_status_date ON public_complaints(status, created_at);
CREATE INDEX IF NOT EXISTS idx_feedback_type_date ON feedbacks(feedback_type, created_at);
CREATE INDEX IF NOT EXISTS idx_public_feedback_type_date ON public_feedback(feedback_type, created_at);
CREATE INDEX IF NOT EXISTS idx_ratings_employee_date ON ratings(employee_id, created_at);
CREATE INDEX IF NOT EXISTS idx_public_ratings_dept_date ON public_ratings(department, created_at);

-- Full-text search indexes
CREATE FULLTEXT INDEX IF NOT EXISTS ft_complaint_description ON complaints(description_en, description_am, description_af);
CREATE FULLTEXT INDEX IF NOT EXISTS ft_public_complaint_description ON public_complaints(complaint_description);
CREATE FULLTEXT INDEX IF NOT EXISTS ft_feedback_comments ON feedbacks(comment_en, comment_am, comment_af);
CREATE FULLTEXT INDEX IF NOT EXISTS ft_public_feedback_text ON public_feedback(feedback_text, subject);
CREATE FULLTEXT INDEX IF NOT EXISTS ft_employee_names ON employees(first_name_en, middle_name_en, last_name_en);

-- =====================================
-- SAMPLE DATA INSERTION
-- =====================================

-- Insert sample departments
INSERT INTO departments (name_en, name_am, name_af, code, description_en, contact_email, contact_phone, head_name) VALUES
('Information Technology', 'ኢንፎርሜሽን ቴክኖሎጂ', 'Teeknooloojii Odeeffannoo', 'IT', 'Information Technology Department', '<EMAIL>', '+251911000001', 'Dr. Abebe Kebede'),
('Human Resources', 'የሰው ሃይል ማሰማራት', 'Humna Namaa', 'HR', 'Human Resources Department', '<EMAIL>', '+251911000002', 'Ato Desta Tesfaye'),
('Finance', 'ፋይናንስ', 'Faayinaansii', 'FIN', 'Finance Department', '<EMAIL>', '+251911000003', 'W/ro Tigist Alemu'),
('Public Relations', 'ህዝብ ግንኙነት', 'Hariiroo Uummataaf', 'PR', 'Public Relations Department', '<EMAIL>', '+251911000004', 'Ato Girma Wolde'),
('Customer Service', 'የደንበኛ አገልግሎት', 'Tajaajila Maamilaa', 'CS', 'Customer Service Department', '<EMAIL>', '+251911000005', 'W/ro Meron Tadesse');

-- Insert sample admin users
INSERT INTO admins (username, password, email, role, first_name, last_name, city, subcity, department, is_active) VALUES
('superadmin', '$2b$10$rOzJbHZHKGOdBUjwMJjWnOxLOVlKwR.LJ1iKaAl3nQj5FwJXkuLAm', '<EMAIL>', 'SuperAdmin', 'System', 'Administrator', 'Addis Ababa', 'Bole', 'IT', true),
('bole_admin', '$2b$10$rOzJbHZHKGOdBUjwMJjWnOxLOVlKwR.LJ1iKaAl3nQj5FwJXkuLAm', '<EMAIL>', 'SubCityAdmin', 'Bole', 'Admin', 'Addis Ababa', 'Bole', 'Administration', true),
('kirkos_admin', '$2b$10$rOzJbHZHKGOdBUjwMJjWnOxLOVlKwR.LJ1iKaAl3nQj5FwJXkuLAm', '<EMAIL>', 'SubCityAdmin', 'Kirkos', 'Admin', 'Addis Ababa', 'Kirkos', 'Administration', true),
('it_admin', '$2b$10$rOzJbHZHKGOdBUjwMJjWnOxLOVlKwR.LJ1iKaAl3nQj5FwJXkuLAm', '<EMAIL>', 'Admin', 'IT', 'Manager', 'Addis Ababa', 'Bole', 'IT', true),
('hr_admin', '$2b$10$rOzJbHZHKGOdBUjwMJjWnOxLOVlKwR.LJ1iKaAl3nQj5FwJXkuLAm', '<EMAIL>', 'Admin', 'HR', 'Manager', 'Addis Ababa', 'Kirkos', 'HR', true);

-- Insert sample offices
INSERT INTO offices (name, name_amharic, name_afan_oromo, office_number, department_id, description, floor, location, phone, email, services_offered) VALUES
('IT Support Office', 'የ IT ድጋፍ ጽ/ቤት', 'Waajjira Deeggarsa IT', 'IT-101', 1, 'Technical support and IT services', '1st Floor', 'Building A, Room 101', '+251911100001', '<EMAIL>', 'Hardware support, Software installation, Network troubleshooting'),
('HR Services Office', 'የሰው ሃይል አገልግሎት ጽ/ቤት', 'Waajjira Tajaajila HR', 'HR-201', 2, 'Human resources services and employee support', '2nd Floor', 'Building B, Room 201', '+251911100002', '<EMAIL>', 'Employee registration, Benefits administration, Training coordination'),
('Finance Office', 'የፋይናንስ ጽ/ቤት', 'Waajjira Faayinaansii', 'FIN-301', 3, 'Financial services and budget management', '3rd Floor', 'Building C, Room 301', '+251911100003', '<EMAIL>', 'Budget planning, Payment processing, Financial reporting'),
('Public Information Desk', 'የህዝብ መረጃ ጠረጴዛ', 'Teessoo Odeeffannoo Uummataaf', 'PR-401', 4, 'Public information and communication services', 'Ground Floor', 'Building A, Lobby', '+251911100004', '<EMAIL>', 'Information provision, Document requests, Public inquiries'),
('Customer Service Center', 'የደንበኛ አገልግሎት ማዕከል', 'Giddugala Tajaajila Maamilaa', 'CS-501', 5, 'Customer service and complaint handling', 'Ground Floor', 'Building B, Main Hall', '+251911100005', '<EMAIL>', 'Complaint handling, Service requests, Customer support');

-- Insert sample employees
INSERT INTO employees (employee_id, first_name_en, first_name_am, first_name_af, last_name_en, last_name_am, last_name_af, office_id, position_en, position_am, position_af, department_en, department_am, department_af, section, city, subcity, email, phone, years_of_service, is_active) VALUES
('EMP001', 'Abebe', 'አበበ', 'Abaabaa', 'Kebede', 'ከበደ', 'Kaabadaa', 1, 'IT Support Specialist', 'የ IT ድጋፍ ባለሙያ', 'Ogeessa Deeggarsa IT', 'Information Technology', 'ኢንፎርሜሽን ቴክኖሎጂ', 'Teeknooloojii Odeeffannoo', 'Bole Subcity', 'Addis Ababa', 'Bole', '<EMAIL>', '+251911200001', 3, true),
('EMP002', 'Tigist', 'ትግስት', 'Tigistii', 'Alemu', 'ዓለሙ', 'Aalamuu', 2, 'HR Officer', 'የሰው ሃይል ኦፊሰር', 'Ofiisara Humna Namaa', 'Human Resources', 'የሰው ሃይል ማሰማራት', 'Humna Namaa', 'Kirkos Subcity', 'Addis Ababa', 'Kirkos', '<EMAIL>', '+251911200002', 5, true),
('EMP003', 'Desta', 'ደስታ', 'Dastaa', 'Tesfaye', 'ተስፋዬ', 'Tasfaayee', 3, 'Finance Analyst', 'የፋይናንስ ተንታኝ', 'Xiinxala Faayinaansii', 'Finance', 'ፋይናንስ', 'Faayinaansii', 'Bole Subcity', 'Addis Ababa', 'Bole', '<EMAIL>', '+251911200003', 4, true),
('EMP004', 'Meron', 'መሮን', 'Maroonii', 'Tadesse', 'ታደሰ', 'Tadasaa', 4, 'Public Relations Officer', 'የህዝብ ግንኙነት ኦፊሰር', 'Ofiisara Hariiroo Uummataaf', 'Public Relations', 'ህዝብ ግንኙነት', 'Hariiroo Uummataaf', 'Kirkos Subcity', 'Addis Ababa', 'Kirkos', '<EMAIL>', '+251911200004', 2, true),
('EMP005', 'Girma', 'ግርማ', 'Girmaa', 'Wolde', 'ወልደ', 'Waldaa', 5, 'Customer Service Representative', 'የደንበኛ አገልግሎት ተወካይ', 'Bakka Buaa Tajaajila Maamilaa', 'Customer Service', 'የደንበኛ አገልግሎት', 'Tajaajila Maamilaa', 'Bole Subcity', 'Addis Ababa', 'Bole', '<EMAIL>', '+251911200005', 1, true);

-- Reset foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================
-- IMPORTANT NOTES FOR HOSTING
-- =====================================

/*
DEPLOYMENT CHECKLIST:

1. DATABASE SETUP:
   - Create a new MySQL database on your hosting server
   - Copy and paste this ENTIRE script into your database manager
   - Execute the script to create all tables and sample data

2. ENVIRONMENT CONFIGURATION:
   Create a .env file in your application root with these variables:
   
   NODE_ENV=production
   PORT=4000
   
   # Database Configuration
   DB_HOST=your_database_host
   DB_PORT=3306
   DB_NAME=your_database_name
   DB_USER=your_database_username
   DB_PASSWORD=your_database_password
   
   # Security
   JWT_SECRET=your_jwt_secret_key_here
   
   # CORS Origins (add your frontend domain)
   ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

3. DEFAULT ADMIN CREDENTIALS:
   - Username: superadmin
   - Password: password123
   - Email: <EMAIL>
   
   ⚠️ IMPORTANT: Change the default password immediately after first login!

4. FILE UPLOAD DIRECTORIES:
   Ensure these directories exist and are writable:
   - /Uploads/voice_complaints/
   - /Uploads/profile_pictures/

5. FEATURES INCLUDED:
   ✅ Multi-language support (English, Amharic, Afan Oromo)
   ✅ Role-based admin access control
   ✅ Public citizen complaint system
   ✅ Service rating and feedback system
   ✅ Employee directory
   ✅ File upload support
   ✅ Performance-optimized indexes
   ✅ Full-text search capabilities

6. API ENDPOINTS:
   Admin Portal: /api/admin/
   Public Portal: /api/
   Health Check: /health

7. SECURITY FEATURES:
   ✅ Password hashing with bcrypt
   ✅ JWT token authentication
   ✅ Input validation and sanitization
   ✅ Rate limiting
   ✅ CORS protection
   ✅ SQL injection prevention

8. PRODUCTION OPTIMIZATION:
   ✅ Database indexes for performance
   ✅ Compressed responses
   ✅ Security headers with Helmet
   ✅ Optimized queries
   ✅ Error handling and logging
*/ 