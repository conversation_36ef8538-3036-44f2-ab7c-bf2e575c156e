version: "3.8"

services:
  # MySQL Database Service
  mysql:
    image: mysql:8.0
    container_name: office_management_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-123456789@abc}
      MYSQL_DATABASE: ${DB_NAME:-office}

    ports:
      - "${DB_PORT:-3307}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - office_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 30s

  # Office Management Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: office_management_app
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${DB_NAME:-office}
      DB_USER: ${DB_USER:-root}
      DB_PASSWORD: ${DB_PASSWORD:-123456789@abc}
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
      PORT: ${APP_PORT:-4000}
    ports:
      - "${APP_PORT:-4000}:4000"
    volumes:
      - uploads_data:/app/Uploads
    networks:
      - office_network
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:4000/api/employees",
        ]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 60s

networks:
  office_network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  uploads_data:
    driver: local
