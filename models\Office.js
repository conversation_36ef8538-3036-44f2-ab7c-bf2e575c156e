const { DataTypes } = require("sequelize");
const sequelize = require("../config/database");

const Office = sequelize.define(
  "Office",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },

    name: { type: DataTypes.STRING(100), allowNull: false },
    name_amharic: { type: DataTypes.STRING(100), allowNull: true },
    office_number: { type: DataTypes.STRING(20), allowNull: true },

    // Department association
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "departments",
        key: "id",
      },
    },

    // Office details
    description: { type: DataTypes.TEXT, allowNull: true },
    floor: { type: DataTypes.STRING(10), allowNull: true },
    location: { type: DataTypes.STRING(200), allowNull: true },

    // Office status
    is_active: { type: DataTypes.BOOLEAN, defaultValue: true },

    // Contact information
    phone: { type: DataTypes.STRING(15), allowNull: true },
    email: { type: DataTypes.STRING(100), allowNull: true },
  },
  {
    tableName: "offices",
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ["department_id"] },
      { fields: ["name"] },
      { fields: ["office_number"] },
      { fields: ["is_active"] },
    ],
  }
);

module.exports = Office;
